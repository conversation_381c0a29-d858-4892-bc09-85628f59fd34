// Simple test to check if our modules compile
package main

import (
	"context"
	"fmt"
	"log"

	"assistant-go/internal/config"
	"assistant-go/internal/core/postgres"
	"assistant-go/internal/core/k8s"
	"assistant-go/internal/core/tasks"
	"assistant-go/internal/core/mcp"
	"assistant-go/pkg/cybertheme"
)

func main() {
	fmt.Println("Testing module compilation...")

	// Test config
	cfg, err := config.Load()
	if err != nil {
		log.Printf("Config load error (expected): %v", err)
	} else {
		fmt.Printf("Config loaded: %s v%s\n", cfg.App.Name, cfg.App.Version)
	}

	// Test cybertheme
	theme := cybertheme.NewCyberTheme()
	fmt.Printf("Theme created with primary color: %v\n", theme.Color("primary", 0))

	// Test modules
	ctx := context.Background()

	// Test postgres module
	pgManager := postgres.NewManager()
	if err := pgManager.Initialize(ctx, cfg); err != nil {
		log.Printf("Postgres init error: %v", err)
	} else {
		fmt.Println("Postgres manager initialized")
	}

	// Test k8s module
	k8sManager := k8s.NewManager()
	if err := k8sManager.Initialize(ctx, cfg); err != nil {
		log.Printf("K8s init error: %v", err)
	} else {
		fmt.Println("K8s manager initialized")
	}

	// Test tasks module
	tasksManager := tasks.NewManager()
	if err := tasksManager.Initialize(ctx, cfg); err != nil {
		log.Printf("Tasks init error: %v", err)
	} else {
		fmt.Println("Tasks manager initialized")
	}

	// Test mcp module
	mcpManager := mcp.NewManager()
	if err := mcpManager.Initialize(ctx, cfg); err != nil {
		log.Printf("MCP init error: %v", err)
	} else {
		fmt.Println("MCP manager initialized")
	}

	fmt.Println("All modules compiled successfully!")
}
