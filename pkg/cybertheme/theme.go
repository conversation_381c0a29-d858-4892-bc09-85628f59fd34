// Package cybertheme provides a Material Design 3 compliant cyberpunk theme for Fyne
// Implements the complete design system from fyne.md with blue-dominant cyberpunk aesthetic
package cybertheme

import (
	"fmt"
	"image/color"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/theme"
)

// CyberTheme implements a cyberpunk-styled theme following Material Design 3 principles
type CyberTheme struct {
	// Core color palette
	colors map[fyne.ThemeColorName]color.Color

	// Typography settings
	fonts map[fyne.ThemeVariant]fyne.Resource

	// Spacing and sizing
	sizes map[fyne.ThemeSizeName]float32
}

// Color definitions following fyne.md specifications
var (
	// Primary Interactive - "The Digital Ocean"
	CyberBlue      = color.RGBA{0, 149, 255, 255}    // #0095FF - Primary actions, focus states
	CyberBlueDark  = color.RGBA{0, 105, 180, 255}    // #0069B4 - Hover states
	CyberBlueLight = color.RGBA{51, 181, 255, 255}   // #33B5FF - Active states

	// Information Flow - "The Data Stream"
	CyberGreen       = color.RGBA{0, 255, 136, 255}   // #00FF88 - Primary text, success states
	CyberGreenDim    = color.RGBA{0, 204, 109, 255}   // #00CC6D - Secondary text
	CyberGreenBright = color.RGBA{102, 255, 179, 255} // #66FFB3 - Highlights, new information

	// Accent & Warning - "The Neural Network"
	CyberPurple = color.RGBA{187, 134, 252, 255} // #BB86FC - Special features, AI elements
	CyberOrange = color.RGBA{255, 152, 0, 255}   // #FF9800 - Warnings, important notices
	CyberRed    = color.RGBA{255, 82, 82, 255}   // #FF5252 - Errors, critical states

	// Environment - "The Digital Void"
	VoidBlack   = color.RGBA{18, 18, 18, 255}  // #121212 - Deepest background
	SpaceGray   = color.RGBA{24, 24, 24, 255}  // #181818 - Main background
	SurfaceGray = color.RGBA{36, 36, 36, 255}  // #242424 - Card/Panel backgrounds
	BorderGray  = color.RGBA{48, 48, 48, 255}  // #303030 - Borders, dividers

	// Text colors for maximum contrast
	PureWhite = color.RGBA{255, 255, 255, 255} // #FFFFFF - Button text, high contrast
	TextGray  = color.RGBA{224, 224, 224, 255} // #E0E0E0 - Secondary text
)

// NewCyberTheme creates a new cyberpunk theme instance
func NewCyberTheme() *CyberTheme {
	t := &CyberTheme{
		colors: make(map[fyne.ThemeColorName]color.Color),
		fonts:  make(map[fyne.ThemeVariant]fyne.Resource),
		sizes:  make(map[fyne.ThemeSizeName]float32),
	}

	t.initializeColors()
	t.initializeFonts()
	t.initializeSizes()

	return t
}

// NewCustomCyberTheme creates a cyberpunk theme with custom primary colors
func NewCustomCyberTheme(primary, secondary, background string) *CyberTheme {
	t := NewCyberTheme()

	// Parse and apply custom colors if provided
	if primary != "" {
		if c, err := parseHexColor(primary); err == nil {
			t.colors[theme.ColorNamePrimary] = c
		}
	}

	if secondary != "" {
		if c, err := parseHexColor(secondary); err == nil {
			t.colors[theme.ColorNameSecondary] = c
		}
	}

	if background != "" {
		if c, err := parseHexColor(background); err == nil {
			t.colors[theme.ColorNameBackground] = c
		}
	}

	return t
}

// initializeColors sets up the complete color system
func (t *CyberTheme) initializeColors() {
	// Background colors
	t.colors[theme.ColorNameBackground] = VoidBlack
	t.colors[theme.ColorNameOverlayBackground] = SurfaceGray

	// Primary colors
	t.colors[theme.ColorNamePrimary] = CyberBlue

	// Text colors - Pure white for maximum contrast
	t.colors[theme.ColorNameForeground] = PureWhite
	t.colors[theme.ColorNameOnPrimary] = PureWhite
	t.colors[theme.ColorNameOnSecondary] = VoidBlack

	// Interactive states
	t.colors[theme.ColorNameHover] = CyberBlueLight
	t.colors[theme.ColorNamePressed] = CyberBlueDark
	t.colors[theme.ColorNameFocus] = CyberBlue
	t.colors[theme.ColorNameSelection] = CyberBlue

	// Status colors
	t.colors[theme.ColorNameSuccess] = CyberGreen
	t.colors[theme.ColorNameWarning] = CyberOrange
	t.colors[theme.ColorNameError] = CyberRed

	// Surface colors
	t.colors[theme.ColorNameInputBackground] = SpaceGray
	t.colors[theme.ColorNameMenuBackground] = SurfaceGray
	t.colors[theme.ColorNameSeparator] = BorderGray

	// Disabled states
	t.colors[theme.ColorNameDisabled] = BorderGray
	t.colors[theme.ColorNameDisabledButton] = BorderGray

	// Shadow and elevation
	t.colors[theme.ColorNameShadow] = color.RGBA{0, 0, 0, 100}
}

// initializeFonts sets up the typography system
func (t *CyberTheme) initializeFonts() {
	// Use default Fyne fonts for now
	// TODO: Load JetBrains Mono and Inter fonts as specified in fyne.md
	t.fonts[theme.VariantRegular] = theme.DefaultTheme().Font(fyne.TextStyle{})
	t.fonts[theme.VariantBold] = theme.DefaultTheme().Font(fyne.TextStyle{Bold: true})
	t.fonts[theme.VariantItalic] = theme.DefaultTheme().Font(fyne.TextStyle{Italic: true})
	t.fonts[theme.VariantMonospace] = theme.DefaultTheme().Font(fyne.TextStyle{Monospace: true})
}

// initializeSizes sets up the spacing and sizing system following Material Design 3
func (t *CyberTheme) initializeSizes() {
	// Spacing system (8dp grid)
	t.sizes[theme.SizeNamePadding] = 16        // SpaceMD
	t.sizes[theme.SizeNameInlineIcon] = 20     // Icon size in buttons
	t.sizes[theme.SizeNameScrollBar] = 16      // Scrollbar width
	t.sizes[theme.SizeNameScrollBarSmall] = 8  // Thin scrollbar
	t.sizes[theme.SizeNameSeparatorThickness] = 1

	// Text sizes following Material Design 3 scale
	t.sizes[theme.SizeNameText] = 14           // BodyMedium
	t.sizes[theme.SizeNameCaptionText] = 12    // BodySmall
	t.sizes[theme.SizeNameHeadingText] = 20    // HeadlineMedium
	t.sizes[theme.SizeNameSubHeadingText] = 16 // BodyLarge

	// Input and button sizes
	t.sizes[theme.SizeNameInputBorder] = 2
	t.sizes[theme.SizeNameInputRadius] = 8
}

// Color returns a color for the given theme color name
func (t *CyberTheme) Color(name fyne.ThemeColorName, variant fyne.ThemeVariant) color.Color {
	if c, ok := t.colors[name]; ok {
		return c
	}

	// Fallback to default theme
	return theme.DefaultTheme().Color(name, variant)
}

// Font returns a font for the given text style
func (t *CyberTheme) Font(style fyne.TextStyle) fyne.Resource {
	variant := theme.VariantRegular

	if style.Bold {
		variant = theme.VariantBold
	} else if style.Italic {
		variant = theme.VariantItalic
	} else if style.Monospace {
		variant = theme.VariantMonospace
	}

	if f, ok := t.fonts[variant]; ok {
		return f
	}

	// Fallback to default theme
	return theme.DefaultTheme().Font(style)
}

// Size returns a size for the given theme size name
func (t *CyberTheme) Size(name fyne.ThemeSizeName) float32 {
	if s, ok := t.sizes[name]; ok {
		return s
	}

	// Fallback to default theme
	return theme.DefaultTheme().Size(name)
}

// Icon returns an icon for the given theme icon name
func (t *CyberTheme) Icon(name fyne.ThemeIconName) fyne.Resource {
	// Use default icons for now
	// TODO: Create custom cyberpunk icon set
	return theme.DefaultTheme().Icon(name)
}

// parseHexColor parses a hex color string to color.Color
func parseHexColor(hex string) (color.Color, error) {
	// Remove # prefix if present
	if len(hex) > 0 && hex[0] == '#' {
		hex = hex[1:]
	}

	// Parse hex values
	if len(hex) != 6 {
		return nil, fyne.NewError("invalid hex color format")
	}

	var r, g, b uint8
	if _, err := fmt.Sscanf(hex, "%02x%02x%02x", &r, &g, &b); err != nil {
		return nil, err
	}

	return color.RGBA{r, g, b, 255}, nil
}
