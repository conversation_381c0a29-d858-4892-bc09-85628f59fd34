// Package cybertheme - Custom widgets implementing cyberpunk design system
// Provides CyberButton, CyberInput, and CyberCard components with Material Design 3 compliance
package cybertheme

import (
	"image/color"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/canvas"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"
)

// CyberButton implements a cyberpunk-styled button with pure white bold text
type CyberButton struct {
	widget.Button

	// Visual properties
	variant    ButtonVariant
	size       ButtonSize
	importance ButtonImportance
}

// ButtonVariant defines the visual style of the button
type ButtonVariant int

const (
	ButtonPrimary ButtonVariant = iota // Blue background, for main actions
	ButtonSecondary                    // Outlined, for secondary actions
	ButtonGhost                        // Transparent, for tertiary actions
	ButtonDanger                       // Red, for destructive actions
)

// ButtonSize defines the size of the button
type ButtonSize int

const (
	ButtonLarge ButtonSize = iota // Height: 48, PaddingX: 24, FontSize: 16
	ButtonMedium                  // Height: 40, PaddingX: 20, FontSize: 14
	ButtonSmall                   // Height: 32, PaddingX: 16, FontSize: 12
)

// ButtonImportance defines the visual importance level
type ButtonImportance int

const (
	ButtonHigh ButtonImportance = iota   // High contrast, bold styling
	ButtonMediumImportance               // Medium contrast
	ButtonLow                            // Low contrast, subtle
)

// NewCyberButton creates a new cyberpunk-styled button
func NewCyberButton(text string, tapped func()) *CyberButton {
	btn := &CyberButton{
		Button:     *widget.NewButton(text, tapped),
		variant:    ButtonPrimary,
		size:       ButtonMedium,
		importance: ButtonHigh,
	}

	btn.ExtendBaseWidget(btn)
	return btn
}

// NewCyberButtonWithVariant creates a button with specific variant
func NewCyberButtonWithVariant(text string, variant ButtonVariant, tapped func()) *CyberButton {
	btn := NewCyberButton(text, tapped)
	btn.variant = variant
	return btn
}

// SetVariant changes the button variant
func (b *CyberButton) SetVariant(variant ButtonVariant) {
	b.variant = variant
	b.Refresh()
}

// SetSize changes the button size
func (b *CyberButton) SetSize(size ButtonSize) {
	b.size = size
	b.Refresh()
}

// SetImportance changes the button importance level
func (b *CyberButton) SetImportance(importance ButtonImportance) {
	b.importance = importance
	b.Refresh()
}

// CreateRenderer creates the custom renderer for CyberButton
func (b *CyberButton) CreateRenderer() fyne.WidgetRenderer {
	return &cyberButtonRenderer{
		button: b,
		bg:     canvas.NewRectangle(b.getBackgroundColor()),
		text:   canvas.NewText(b.Text, b.getTextColor()),
	}
}

// getBackgroundColor returns the appropriate background color
func (b *CyberButton) getBackgroundColor() color.Color {
	switch b.variant {
	case ButtonPrimary:
		if b.Disabled() {
			return BorderGray
		}
		return CyberBlue
	case ButtonSecondary:
		return color.Transparent
	case ButtonGhost:
		return color.Transparent
	case ButtonDanger:
		if b.Disabled() {
			return BorderGray
		}
		return CyberRed
	default:
		return CyberBlue
	}
}

// getTextColor returns the appropriate text color (always pure white for contrast)
func (b *CyberButton) getTextColor() color.Color {
	if b.Disabled() {
		return CyberGreenDim
	}
	return PureWhite // Always pure white for maximum contrast
}

// getBorderColor returns the appropriate border color
func (b *CyberButton) getBorderColor() color.Color {
	switch b.variant {
	case ButtonSecondary:
		return CyberBlue
	case ButtonGhost:
		return color.Transparent
	default:
		return color.Transparent
	}
}

// cyberButtonRenderer implements the custom renderer
type cyberButtonRenderer struct {
	button *CyberButton
	bg     *canvas.Rectangle
	text   *canvas.Text
}

func (r *cyberButtonRenderer) Layout(size fyne.Size) {
	r.bg.Resize(size)

	// Set text properties based on button size and importance
	r.text.TextStyle = fyne.TextStyle{Bold: true} // Always bold for maximum contrast
	r.text.Alignment = fyne.TextAlignCenter

	switch r.button.size {
	case ButtonLarge:
		r.text.TextSize = 16
	case ButtonMedium:
		r.text.TextSize = 14
	case ButtonSmall:
		r.text.TextSize = 12
	}

	// Center the text
	textSize := r.text.MinSize()
	r.text.Move(fyne.NewPos((size.Width-textSize.Width)/2, (size.Height-textSize.Height)/2))
	r.text.Resize(textSize)
}

func (r *cyberButtonRenderer) MinSize() fyne.Size {
	switch r.button.size {
	case ButtonLarge:
		return fyne.NewSize(120, 48)
	case ButtonMedium:
		return fyne.NewSize(100, 40)
	case ButtonSmall:
		return fyne.NewSize(80, 32)
	default:
		return fyne.NewSize(100, 40)
	}
}

func (r *cyberButtonRenderer) Refresh() {
	r.bg.FillColor = r.button.getBackgroundColor()
	r.text.Color = r.button.getTextColor()
	r.text.Text = r.button.Text

	// Add border for secondary buttons
	if r.button.variant == ButtonSecondary {
		r.bg.StrokeColor = r.button.getBorderColor()
		r.bg.StrokeWidth = 2
	} else {
		r.bg.StrokeWidth = 0
	}

	r.bg.Refresh()
	r.text.Refresh()
}

func (r *cyberButtonRenderer) Objects() []fyne.CanvasObject {
	return []fyne.CanvasObject{r.bg, r.text}
}

func (r *cyberButtonRenderer) Destroy() {}

// CyberInput implements a cyberpunk-styled input field
type CyberInput struct {
	widget.Entry

	// Visual state
	hasError   bool
	hasSuccess bool
}

// NewCyberInput creates a new cyberpunk-styled input field
func NewCyberInput() *CyberInput {
	input := &CyberInput{
		Entry: *widget.NewEntry(),
	}

	input.ExtendBaseWidget(input)
	return input
}

// SetError sets the input to error state
func (i *CyberInput) SetError(hasError bool) {
	i.hasError = hasError
	i.hasSuccess = false
	i.Refresh()
}

// SetSuccess sets the input to success state
func (i *CyberInput) SetSuccess(hasSuccess bool) {
	i.hasSuccess = hasSuccess
	i.hasError = false
	i.Refresh()
}

// CyberCard implements a cyberpunk-styled card container
type CyberCard struct {
	*fyne.Container

	// Visual properties
	elevation CardElevation
	hoverable bool
	clickable bool
	onTap     func()
}

// CardElevation defines the shadow/elevation level
type CardElevation int

const (
	CardFlat CardElevation = iota     // No shadow
	CardRaised                       // Subtle shadow
	CardFloating                     // Prominent shadow
)

// NewCyberCard creates a new cyberpunk-styled card
func NewCyberCard(content ...fyne.CanvasObject) *CyberCard {
	card := &CyberCard{
		Container: container.NewBorder(nil, nil, nil, nil, content...),
		elevation: CardRaised,
	}

	return card
}

// NewCyberCardWithElevation creates a card with specific elevation
func NewCyberCardWithElevation(elevation CardElevation, content ...fyne.CanvasObject) *CyberCard {
	card := NewCyberCard(content...)
	card.elevation = elevation
	return card
}

// SetElevation changes the card elevation
func (c *CyberCard) SetElevation(elevation CardElevation) {
	c.elevation = elevation
	c.Refresh()
}

// SetHoverable enables/disables hover effects
func (c *CyberCard) SetHoverable(hoverable bool) {
	c.hoverable = hoverable
}

// SetClickable makes the card clickable with the given callback
func (c *CyberCard) SetClickable(onTap func()) {
	c.clickable = true
	c.onTap = onTap
}

// StatusIndicator implements a cyberpunk-styled status indicator
type StatusIndicator struct {
	widget.BaseWidget

	status    StatusType
	mode      DisplayMode
	text      string
	animation bool
}

// StatusType defines the type of status
type StatusType int

const (
	StatusOnline StatusType = iota // Green, pulse animation
	StatusOffline                  // Gray, no animation
	StatusLoading                  // Blue, spin animation
	StatusWarning                  // Orange, pulse animation
	StatusError                    // Red, fast pulse animation
)

// DisplayMode defines how the status is displayed
type DisplayMode int

const (
	DisplayDot DisplayMode = iota // Small dot only
	DisplayIcon                   // Icon with optional text
	DisplayBadge                  // Full badge with text
)

// NewStatusIndicator creates a new status indicator
func NewStatusIndicator(status StatusType, mode DisplayMode, text string) *StatusIndicator {
	indicator := &StatusIndicator{
		status:    status,
		mode:      mode,
		text:      text,
		animation: true,
	}

	indicator.ExtendBaseWidget(indicator)
	return indicator
}

// SetStatus updates the status type
func (s *StatusIndicator) SetStatus(status StatusType) {
	s.status = status
	s.Refresh()
}

// SetText updates the status text
func (s *StatusIndicator) SetText(text string) {
	s.text = text
	s.Refresh()
}

// SetAnimation enables/disables animations
func (s *StatusIndicator) SetAnimation(enabled bool) {
	s.animation = enabled
	s.Refresh()
}

// CreateRenderer creates the renderer for StatusIndicator
func (s *StatusIndicator) CreateRenderer() fyne.WidgetRenderer {
	return &statusIndicatorRenderer{
		indicator: s,
		circle:    canvas.NewCircle(s.getStatusColor()),
		text:      canvas.NewText(s.text, PureWhite),
	}
}

// getStatusColor returns the color for the current status
func (s *StatusIndicator) getStatusColor() color.Color {
	switch s.status {
	case StatusOnline:
		return CyberGreen
	case StatusOffline:
		return BorderGray
	case StatusLoading:
		return CyberBlue
	case StatusWarning:
		return CyberOrange
	case StatusError:
		return CyberRed
	default:
		return BorderGray
	}
}

// statusIndicatorRenderer implements the renderer
type statusIndicatorRenderer struct {
	indicator *StatusIndicator
	circle    *canvas.Circle
	text      *canvas.Text
}

func (r *statusIndicatorRenderer) Layout(size fyne.Size) {
	switch r.indicator.mode {
	case DisplayDot:
		r.circle.Resize(fyne.NewSize(8, 8))
		r.circle.Move(fyne.NewPos((size.Width-8)/2, (size.Height-8)/2))
	case DisplayIcon:
		r.circle.Resize(fyne.NewSize(16, 16))
		r.circle.Move(fyne.NewPos(0, (size.Height-16)/2))
		if r.indicator.text != "" {
			textSize := r.text.MinSize()
			r.text.Move(fyne.NewPos(20, (size.Height-textSize.Height)/2))
			r.text.Resize(textSize)
		}
	case DisplayBadge:
		r.circle.Resize(fyne.NewSize(24, 24))
		r.circle.Move(fyne.NewPos(0, (size.Height-24)/2))
		if r.indicator.text != "" {
			textSize := r.text.MinSize()
			r.text.Move(fyne.NewPos(28, (size.Height-textSize.Height)/2))
			r.text.Resize(textSize)
		}
	}
}

func (r *statusIndicatorRenderer) MinSize() fyne.Size {
	switch r.indicator.mode {
	case DisplayDot:
		return fyne.NewSize(8, 8)
	case DisplayIcon:
		if r.indicator.text != "" {
			textSize := r.text.MinSize()
			return fyne.NewSize(20+textSize.Width, fyne.Max(16, textSize.Height))
		}
		return fyne.NewSize(16, 16)
	case DisplayBadge:
		if r.indicator.text != "" {
			textSize := r.text.MinSize()
			return fyne.NewSize(28+textSize.Width, fyne.Max(24, textSize.Height))
		}
		return fyne.NewSize(24, 24)
	default:
		return fyne.NewSize(16, 16)
	}
}

func (r *statusIndicatorRenderer) Refresh() {
	r.circle.FillColor = r.indicator.getStatusColor()
	r.text.Text = r.indicator.text
	r.circle.Refresh()
	r.text.Refresh()
}

func (r *statusIndicatorRenderer) Objects() []fyne.CanvasObject {
	if r.indicator.text != "" && r.indicator.mode != DisplayDot {
		return []fyne.CanvasObject{r.circle, r.text}
	}
	return []fyne.CanvasObject{r.circle}
}

func (r *statusIndicatorRenderer) Destroy() {}
