// Package views - Navigation component for module switching
// Implements the 8-module navigation system as specified in fyne.md
package views

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"

	"assistant-go/pkg/cybertheme"
)

// Navigation represents the module navigation component
type Navigation struct {
	theme *cybertheme.CyberTheme

	// UI components
	content       *fyne.Container
	moduleButtons map[string]*cybertheme.CyberButton
	activeModule  string

	// Callback for module selection
	onModuleSelected func(string)
}

// ModuleInfo represents information about a module
type ModuleInfo struct {
	Name        string
	DisplayName string
	Icon        string
	Description string
}

// GetModules returns the 8 modules as specified in fyne.md
func GetModules() []ModuleInfo {
	return []ModuleInfo{
		{
			Name:        "docker",
			DisplayName: "Docker",
			Icon:        "🐳",
			Description: "Container Management",
		},
		{
			Name:        "chat",
			DisplayName: "Chat",
			Icon:        "💬",
			Description: "AI Assistant",
		},
		{
			Name:        "database",
			DisplayName: "Database",
			Icon:        "🗄️",
			Description: "PostgreSQL Management",
		},
		{
			Name:        "k8s",
			DisplayName: "K8s",
			Icon:        "☸️",
			Description: "Kubernetes Cluster",
		},
		{
			Name:        "mcp",
			DisplayName: "MCP",
			Icon:        "🔗",
			Description: "Model Context Protocol",
		},
		{
			Name:        "search",
			DisplayName: "Search",
			Icon:        "🔍",
			Description: "SearXNG Integration",
		},
		{
			Name:        "tasks",
			DisplayName: "Tasks",
			Icon:        "📋",
			Description: "Build & Task Runner",
		},
		{
			Name:        "agent",
			DisplayName: "Agent",
			Icon:        "🤖",
			Description: "AI Agent Control",
		},
	}
}

// NewNavigation creates a new navigation component
func NewNavigation(theme *cybertheme.CyberTheme, onModuleSelected func(string)) *Navigation {
	n := &Navigation{
		theme:            theme,
		moduleButtons:    make(map[string]*cybertheme.CyberButton),
		onModuleSelected: onModuleSelected,
		activeModule:     "database", // Default active module
	}

	n.createComponents()
	n.setupLayout()

	return n
}

// createComponents creates all navigation components
func (n *Navigation) createComponents() {
	modules := GetModules()

	for _, module := range modules {
		// Create button with icon and text
		buttonText := module.Icon + " " + module.DisplayName
		
		// Create button with proper variant
		button := cybertheme.NewCyberButtonWithVariant(
			buttonText,
			cybertheme.ButtonSecondary, // Start as secondary
			n.createModuleHandler(module.Name),
		)
		
		// Set button size and importance for navigation
		button.SetSize(cybertheme.ButtonMedium)
		button.SetImportance(cybertheme.ButtonHigh)
		
		n.moduleButtons[module.Name] = button
	}

	// Set the default active module
	n.setActiveButton("database")
}

// setupLayout creates the navigation layout
func (n *Navigation) setupLayout() {
	// Create horizontal container for module buttons
	buttonContainer := container.NewHBox()

	// Add buttons in the order specified in fyne.md
	modules := GetModules()
	for _, module := range modules {
		if button, exists := n.moduleButtons[module.Name]; exists {
			buttonContainer.Add(button)
		}
	}

	// Wrap in a container with proper spacing and height
	n.content = container.NewBorder(
		nil, // top
		nil, // bottom
		nil, // left
		nil, // right
		buttonContainer, // center
	)

	// Set height as specified in fyne.md (64px)
	n.content.Resize(fyne.NewSize(0, 64))
}

// createModuleHandler creates a handler function for module selection
func (n *Navigation) createModuleHandler(moduleName string) func() {
	return func() {
		n.selectModule(moduleName)
	}
}

// selectModule handles module selection
func (n *Navigation) selectModule(moduleName string) {
	// Use fyne.Do for thread-safe UI updates
	fyne.Do(func() {
		// Update active module
		n.setActiveButton(moduleName)
		n.activeModule = moduleName

		// Notify parent component
		if n.onModuleSelected != nil {
			n.onModuleSelected(moduleName)
		}
	})
}

// setActiveButton updates the visual state of buttons
func (n *Navigation) setActiveButton(activeModule string) {
	for moduleName, button := range n.moduleButtons {
		if moduleName == activeModule {
			// Set active button to primary variant
			button.SetVariant(cybertheme.ButtonPrimary)
		} else {
			// Set inactive buttons to secondary variant
			button.SetVariant(cybertheme.ButtonSecondary)
		}
	}
}

// SetActiveModule sets the active module (called from parent)
func (n *Navigation) SetActiveModule(moduleName string) {
	fyne.Do(func() {
		n.setActiveButton(moduleName)
		n.activeModule = moduleName
	})
}

// GetActiveModule returns the currently active module
func (n *Navigation) GetActiveModule() string {
	return n.activeModule
}

// Content returns the navigation content
func (n *Navigation) Content() fyne.CanvasObject {
	return n.content
}

// GetHeight returns the navigation height
func (n *Navigation) GetHeight() float32 {
	return 64 // As specified in fyne.md
}

// EnableModule enables/disables a specific module button
func (n *Navigation) EnableModule(moduleName string, enabled bool) {
	fyne.Do(func() {
		if button, exists := n.moduleButtons[moduleName]; exists {
			if enabled {
				button.Enable()
			} else {
				button.Disable()
			}
		}
	})
}

// SetModuleStatus updates the visual status of a module
func (n *Navigation) SetModuleStatus(moduleName string, status cybertheme.StatusType) {
	// This could be used to show status indicators on module buttons
	// For example, showing a red dot for modules with errors
	// Implementation depends on extending CyberButton to support status indicators
}

// GetModuleButton returns a specific module button (for testing or advanced customization)
func (n *Navigation) GetModuleButton(moduleName string) *cybertheme.CyberButton {
	return n.moduleButtons[moduleName]
}
