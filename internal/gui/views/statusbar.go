// Package views - Status bar component
// Implements the bottom status bar with system information as specified in fyne.md
package views

import (
	"fmt"
	"runtime"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"assistant-go/internal/config"
	"assistant-go/pkg/cybertheme"
)

// StatusBar represents the application status bar component
type StatusBar struct {
	config *config.Config
	theme  *cybertheme.CyberTheme

	// UI components
	content            *fyne.Container
	connectionStatus   *cybertheme.StatusIndicator
	aiStatus          *cybertheme.StatusIndicator
	messageLabel      *widget.Label
	memoryLabel       *widget.Label
	cpuLabel          *widget.Label
	timeLabel         *widget.Label

	// Status tracking
	currentMessage string
	currentLevel   string
}

// NewStatusBar creates a new status bar component
func NewStatusBar(cfg *config.Config, theme *cybertheme.CyberTheme) *StatusBar {
	sb := &StatusBar{
		config:         cfg,
		theme:          theme,
		currentMessage: "Ready",
		currentLevel:   "info",
	}

	sb.createComponents()
	sb.setupLayout()
	sb.startSystemMonitoring()

	return sb
}

// createComponents creates all status bar components
func (sb *StatusBar) createComponents() {
	// Connection status indicator
	sb.connectionStatus = cybertheme.NewStatusIndicator(
		cybertheme.StatusOnline,
		cybertheme.DisplayIcon,
		"Connected",
	)

	// AI status indicator
	sb.aiStatus = cybertheme.NewStatusIndicator(
		cybertheme.StatusOnline,
		cybertheme.DisplayIcon,
		"AI Ready",
	)

	// Message label for general status messages
	sb.messageLabel = widget.NewLabel("Ready")

	// System monitoring labels
	sb.memoryLabel = widget.NewLabel("Memory: --")
	sb.memoryLabel.TextStyle = fyne.TextStyle{Monospace: true}

	sb.cpuLabel = widget.NewLabel("CPU: --")
	sb.cpuLabel.TextStyle = fyne.TextStyle{Monospace: true}

	sb.timeLabel = widget.NewLabel(time.Now().Format("15:04:05"))
	sb.timeLabel.TextStyle = fyne.TextStyle{Monospace: true}
}

// setupLayout creates the status bar layout following fyne.md specifications
func (sb *StatusBar) setupLayout() {
	// Left section: Connection and AI status
	leftSection := container.NewHBox(
		sb.connectionStatus,
		widget.NewSeparator(),
		sb.aiStatus,
		widget.NewSeparator(),
		sb.messageLabel,
	)

	// Right section: System info and time
	rightSection := container.NewHBox(
		sb.memoryLabel,
		widget.NewSeparator(),
		sb.cpuLabel,
		widget.NewSeparator(),
		sb.timeLabel,
	)

	// Main status bar layout
	sb.content = container.NewBorder(
		nil, // top
		nil, // bottom
		leftSection,  // left
		rightSection, // right
		nil, // center (empty for now)
	)

	// Set height as specified in fyne.md (32px)
	sb.content.Resize(fyne.NewSize(0, 32))
}

// startSystemMonitoring starts the system monitoring goroutines
func (sb *StatusBar) startSystemMonitoring() {
	// Time updater
	go func() {
		ticker := time.NewTicker(1 * time.Second)
		defer ticker.Stop()

		for range ticker.C {
			currentTime := time.Now().Format("15:04:05")
			// Use fyne.Do for thread-safe UI updates
			fyne.Do(func() {
				sb.timeLabel.SetText(currentTime)
			})
		}
	}()

	// System stats updater
	go func() {
		ticker := time.NewTicker(5 * time.Second)
		defer ticker.Stop()

		for range ticker.C {
			sb.updateSystemStats()
		}
	}()
}

// updateSystemStats updates system statistics
func (sb *StatusBar) updateSystemStats() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// Convert bytes to MB
	memoryMB := float64(m.Alloc) / 1024 / 1024
	memoryText := fmt.Sprintf("Memory: %.1fMB", memoryMB)

	// Get CPU count (simple metric)
	cpuCount := runtime.NumCPU()
	goroutineCount := runtime.NumGoroutine()
	cpuText := fmt.Sprintf("CPU: %d cores, %d goroutines", cpuCount, goroutineCount)

	// Use fyne.Do for thread-safe UI updates
	fyne.Do(func() {
		sb.memoryLabel.SetText(memoryText)
		sb.cpuLabel.SetText(cpuText)
	})
}

// SetMessage updates the status message
func (sb *StatusBar) SetMessage(message, level string) {
	// Use fyne.Do for thread-safe UI updates
	fyne.Do(func() {
		sb.currentMessage = message
		sb.currentLevel = level
		sb.messageLabel.SetText(message)

		// Update message color based on level
		switch level {
		case "error":
			// Red text for errors
			sb.messageLabel.Importance = widget.DangerImportance
		case "warning":
			// Orange text for warnings
			sb.messageLabel.Importance = widget.WarningImportance
		case "success":
			// Green text for success
			sb.messageLabel.Importance = widget.SuccessImportance
		default:
			// Default text color
			sb.messageLabel.Importance = widget.MediumImportance
		}

		sb.messageLabel.Refresh()
	})
}

// SetConnectionStatus updates the connection status
func (sb *StatusBar) SetConnectionStatus(status cybertheme.StatusType, message string) {
	fyne.Do(func() {
		sb.connectionStatus.SetStatus(status)
		sb.connectionStatus.SetText(message)
	})
}

// SetAIStatus updates the AI status
func (sb *StatusBar) SetAIStatus(status cybertheme.StatusType, message string) {
	fyne.Do(func() {
		sb.aiStatus.SetStatus(status)
		sb.aiStatus.SetText(message)
	})
}

// ShowTemporaryMessage shows a temporary message that auto-clears
func (sb *StatusBar) ShowTemporaryMessage(message, level string, duration time.Duration) {
	originalMessage := sb.currentMessage
	originalLevel := sb.currentLevel

	// Set temporary message
	sb.SetMessage(message, level)

	// Clear after duration
	go func() {
		time.Sleep(duration)
		// Use fyne.Do for thread-safe UI updates
		fyne.Do(func() {
			sb.SetMessage(originalMessage, originalLevel)
		})
	}()
}

// ShowProgressMessage shows a message with animated progress indicator
func (sb *StatusBar) ShowProgressMessage(baseMessage string) {
	go func() {
		dots := []string{"", ".", "..", "..."}
		dotIndex := 0

		ticker := time.NewTicker(500 * time.Millisecond)
		defer ticker.Stop()

		// Show progress for 10 seconds max
		timeout := time.After(10 * time.Second)

		for {
			select {
			case <-ticker.C:
				animatedMessage := baseMessage + dots[dotIndex]
				dotIndex = (dotIndex + 1) % len(dots)

				// Use fyne.Do for thread-safe UI updates
				fyne.Do(func() {
					sb.SetMessage(animatedMessage, "processing")
				})

			case <-timeout:
				return
			}
		}
	}()
}

// Content returns the status bar content
func (sb *StatusBar) Content() fyne.CanvasObject {
	return sb.content
}

// GetHeight returns the status bar height
func (sb *StatusBar) GetHeight() float32 {
	return 32 // As specified in fyne.md
}

// GetCurrentMessage returns the current status message
func (sb *StatusBar) GetCurrentMessage() (string, string) {
	return sb.currentMessage, sb.currentLevel
}

// SetSystemInfo updates system information display
func (sb *StatusBar) SetSystemInfo(info map[string]string) {
	fyne.Do(func() {
		if memory, exists := info["memory"]; exists {
			sb.memoryLabel.SetText("Memory: " + memory)
		}
		if cpu, exists := info["cpu"]; exists {
			sb.cpuLabel.SetText("CPU: " + cpu)
		}
	})
}

// Flash briefly highlights the status bar (for notifications)
func (sb *StatusBar) Flash(color cybertheme.StatusType) {
	// TODO: Implement flash animation
	// This could briefly change the background color to indicate notifications
}

// IsConnected returns whether the system is connected
func (sb *StatusBar) IsConnected() bool {
	// Check if connection status is online
	return sb.connectionStatus != nil
}

// IsAIReady returns whether the AI system is ready
func (sb *StatusBar) IsAIReady() bool {
	// Check if AI status is online
	return sb.aiStatus != nil
}
