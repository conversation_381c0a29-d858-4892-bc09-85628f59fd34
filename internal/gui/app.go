// Package gui provides the main GUI application for Assistant-Go
// Implements the complete Fyne interface following fyne.md specifications
package gui

import (
	"fmt"
	"log/slog"
	"os"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"assistant-go/internal/config"
	"assistant-go/internal/gui/views"
	"assistant-go/pkg/cybertheme"
)

// App represents the main GUI application
type App struct {
	// Core components
	fyneApp fyne.App
	window  fyne.Window
	config  *config.Config
	theme   *cybertheme.CyberTheme
	logger  *slog.Logger

	// Layout components
	header     *views.Header
	navigation *views.Navigation
	content    *views.MainContent
	statusBar  *views.StatusBar

	// Module management
	modules map[string]interface{}

	// Current state
	currentModule string
}

// New creates a new GUI application instance
func New(fyneApp fyne.App, cfg *config.Config, modules map[string]interface{}) (*App, error) {
	// Initialize logger
	logger := slog.New(slog.NewJSONHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelInfo,
	}))

	// Create cyberpunk theme
	cyberTheme := cybertheme.NewCustomCyberTheme(
		cfg.Theme.PrimaryColor,
		cfg.Theme.SecondaryColor,
		cfg.Theme.Background,
	)

	// Apply theme to the Fyne app
	fyneApp.Settings().SetTheme(cyberTheme)

	// Create main window
	window := fyneApp.NewWindow("Assistant-Go - Cyberpunk Development Environment")
	window.SetMaster()

	// Set window properties following fyne.md specifications
	window.Resize(fyne.NewSize(1400, 900)) // Optimal size for development work
	window.SetFixedSize(false)             // Allow resizing
	window.CenterOnScreen()

	app := &App{
		fyneApp:       fyneApp,
		window:        window,
		config:        cfg,
		theme:         cyberTheme,
		logger:        logger,
		modules:       modules,
		currentModule: "database", // Default to Database module
	}

	// Initialize GUI components
	if err := app.initializeComponents(); err != nil {
		return nil, fmt.Errorf("failed to initialize GUI components: %w", err)
	}

	// Setup the main layout
	app.setupLayout()

	logger.Info("GUI application initialized successfully")
	return app, nil
}

// initializeComponents creates all GUI components
func (a *App) initializeComponents() error {
	a.logger.Info("Initializing GUI components...")

	// Create header component
	a.header = views.NewHeader(a.config, a.theme)

	// Create navigation component with 8 modules as specified in fyne.md
	a.navigation = views.NewNavigation(a.theme, a.onModuleSelected)

	// Create main content area
	a.content = views.NewMainContent(a.theme)

	// Create status bar
	a.statusBar = views.NewStatusBar(a.config, a.theme)

	// Initialize with default module
	a.switchToModule(a.currentModule)

	a.logger.Info("GUI components initialized successfully")
	return nil
}

// setupLayout creates the main application layout following fyne.md structure
func (a *App) setupLayout() {
	// Main layout structure as specified in fyne.md:
	// +----------------------------------------------------------+
	// | App Header (56px)                                         |
	// +----------------------------------------------------------+
	// | Module Navigation (64px)                                  |
	// +----------------------------------------------------------+
	// | Module Content Area (flex)                                |
	// +----------------------------------------------------------+
	// | Status Bar (32px)                                         |
	// +----------------------------------------------------------+

	mainLayout := container.NewBorder(
		// Top: Header + Navigation
		container.NewVBox(
			a.header.Content(),
			a.navigation.Content(),
		),
		// Bottom: Status Bar
		a.statusBar.Content(),
		// Left: nil
		nil,
		// Right: nil
		nil,
		// Center: Main Content Area
		a.content.Content(),
	)

	a.window.SetContent(mainLayout)
}

// onModuleSelected handles module selection from navigation
func (a *App) onModuleSelected(moduleName string) {
	a.logger.Info("Module selected", "module", moduleName)
	a.switchToModule(moduleName)
}

// switchToModule switches the current module and updates the content area
func (a *App) switchToModule(moduleName string) {
	// Use fyne.Do to ensure thread safety for UI updates
	fyne.Do(func() {
		a.currentModule = moduleName
		a.navigation.SetActiveModule(moduleName)

		// Get module content based on the module name
		moduleContent := a.getModuleContent(moduleName)
		a.content.SetContent(moduleContent)

		// Update status bar
		a.statusBar.SetMessage(fmt.Sprintf("Switched to %s module", moduleName), "info")

		a.logger.Info("Switched to module", "module", moduleName)
	})
}

// getModuleContent returns the content for the specified module
func (a *App) getModuleContent(moduleName string) fyne.CanvasObject {
	switch moduleName {
	case "database":
		return a.getDatabaseContent()
	case "k8s":
		return a.getK8sContent()
	case "docker":
		return a.getDockerContent()
	case "tasks":
		return a.getTasksContent()
	case "mcp":
		return a.getMCPContent()
	case "search":
		return a.getSearchContent()
	case "chat":
		return a.getChatContent()
	case "agent":
		return a.getAgentContent()
	default:
		return a.getPlaceholderContent(moduleName)
	}
}

// getDatabaseContent returns the PostgreSQL module content
func (a *App) getDatabaseContent() fyne.CanvasObject {
	if module, exists := a.modules["postgres"]; exists {
		// Try to get UI from the postgres module
		if postgresModule, ok := module.(interface{ GetUI() fyne.CanvasObject }); ok {
			return postgresModule.GetUI()
		}
	}
	return a.getPlaceholderContent("Database")
}

// getK8sContent returns the Kubernetes module content
func (a *App) getK8sContent() fyne.CanvasObject {
	if module, exists := a.modules["k8s"]; exists {
		// Try to get UI from the k8s module
		if k8sModule, ok := module.(interface{ GetUI() fyne.CanvasObject }); ok {
			return k8sModule.GetUI()
		}
	}
	return a.getPlaceholderContent("Kubernetes")
}

// getDockerContent returns the Docker module content
func (a *App) getDockerContent() fyne.CanvasObject {
	if module, exists := a.modules["docker"]; exists {
		// Try to get UI from the docker module
		if dockerModule, ok := module.(interface{ GetUI() fyne.CanvasObject }); ok {
			return dockerModule.GetUI()
		}
	}
	return a.getPlaceholderContent("Docker")
}

// getTasksContent returns the Tasks module content
func (a *App) getTasksContent() fyne.CanvasObject {
	if module, exists := a.modules["tasks"]; exists {
		// Try to get UI from the tasks module
		if tasksModule, ok := module.(interface{ GetUI() fyne.CanvasObject }); ok {
			return tasksModule.GetUI()
		}
	}
	return a.getPlaceholderContent("Tasks")
}

// getMCPContent returns the MCP module content
func (a *App) getMCPContent() fyne.CanvasObject {
	if module, exists := a.modules["mcp"]; exists {
		// Try to get UI from the mcp module
		if mcpModule, ok := module.(interface{ GetUI() fyne.CanvasObject }); ok {
			return mcpModule.GetUI()
		}
	}
	return a.getPlaceholderContent("MCP")
}

// getSearchContent returns the Search module content (placeholder for now)
func (a *App) getSearchContent() fyne.CanvasObject {
	return a.getPlaceholderContent("Search")
}

// getChatContent returns the Chat module content (placeholder for now)
func (a *App) getChatContent() fyne.CanvasObject {
	return a.getPlaceholderContent("Chat")
}

// getAgentContent returns the Agent module content (placeholder for now)
func (a *App) getAgentContent() fyne.CanvasObject {
	return a.getPlaceholderContent("Agent")
}

// getPlaceholderContent creates a placeholder content for modules not yet implemented
func (a *App) getPlaceholderContent(moduleName string) fyne.CanvasObject {
	content := container.NewVBox(
		widget.NewCard(
			fmt.Sprintf("🚧 %s Module", moduleName),
			"This module is under development",
			widget.NewLabel(fmt.Sprintf("The %s module will be implemented according to the specifications in fyne.md", moduleName)),
		),
		widget.NewSeparator(),
		widget.NewRichTextFromMarkdown(fmt.Sprintf(`
## %s Module Status

This module is currently being developed following the Material Design 3 principles and cyberpunk aesthetic specified in fyne.md.

### Planned Features:
- Full Material Design 3 compliance
- Cyberpunk theme integration
- Real-time data updates
- Responsive layout design
- Proper threading with fyne.Do()
- Bidirectional scrolling support

### Implementation Progress:
- [ ] Core functionality
- [ ] UI components
- [ ] Data integration
- [ ] Testing and validation

Stay tuned for updates!
		`, moduleName)),
	)

	// Wrap in scroll container for overflow handling
	return container.NewScroll(content)
}

// ShowAndRun displays the window and starts the GUI event loop
func (a *App) ShowAndRun() {
	a.logger.Info("Starting GUI application...")
	a.window.ShowAndRun()
}

// Close gracefully closes the GUI application
func (a *App) Close() {
	a.logger.Info("Closing GUI application...")
	a.window.Close()
}

// GetCurrentModule returns the currently active module
func (a *App) GetCurrentModule() string {
	return a.currentModule
}

// SetStatusMessage updates the status bar message
func (a *App) SetStatusMessage(message, level string) {
	if a.statusBar != nil {
		a.statusBar.SetMessage(message, level)
	}
}

// GetWindow returns the main window
func (a *App) GetWindow() fyne.Window {
	return a.window
}

// GetTheme returns the cyberpunk theme
func (a *App) GetTheme() *cybertheme.CyberTheme {
	return a.theme
}
