// Package gui provides the main GUI application for Assistant-Go
// Implements the complete Fyne interface following fyne.md specifications
package gui

import (
	"fmt"
	"log/slog"
	"os"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"assistant-go/internal/config"
	"assistant-go/internal/gui/views"
	"assistant-go/internal/types"
	"assistant-go/pkg/cybertheme"
)

// App represents the main GUI application
type App struct {
	// Core components
	fyneApp fyne.App
	window  fyne.Window
	config  *config.Config
	theme   *cybertheme.CyberTheme
	logger  *slog.Logger

	// Layout components
	header     *views.Header
	navigation *views.Navigation
	content    *views.MainContent
	statusBar  *views.StatusBar

	// Module management
	modules map[string]interface{}

	// Current state
	currentModule string
}

// New creates a new GUI application instance
func New(fyneApp fyne.App, cfg *config.Config, modules map[string]interface{}) (*App, error) {
	// Initialize logger
	logger := slog.New(slog.NewJSONHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelInfo,
	}))

	// Create cyberpunk theme
	cyberTheme := cybertheme.NewCustomCyberTheme(
		cfg.Theme.PrimaryColor,
		cfg.Theme.SecondaryColor,
		cfg.Theme.Background,
	)

	// Apply theme to the Fyne app
	fyneApp.Settings().SetTheme(cyberTheme)

	// Create main window
	window := fyneApp.NewWindow("Assistant-Go - Cyberpunk Development Environment")
	window.SetMaster()

	// Set window properties following fyne.md specifications
	window.Resize(fyne.NewSize(1400, 900)) // Optimal size for development work
	window.SetFixedSize(false)             // Allow resizing
	window.CenterOnScreen()

	app := &App{
		fyneApp:       fyneApp,
		window:        window,
		config:        cfg,
		theme:         cyberTheme,
		logger:        logger,
		modules:       modules,
		currentModule: "dashboard", // Default to Dashboard home screen
	}

	// Initialize GUI components
	if err := app.initializeComponents(); err != nil {
		return nil, fmt.Errorf("failed to initialize GUI components: %w", err)
	}

	// Setup the main layout
	app.setupLayout()

	logger.Info("GUI application initialized successfully")
	return app, nil
}

// initializeComponents creates all GUI components
func (a *App) initializeComponents() error {
	a.logger.Info("Initializing GUI components...")

	// Create header component
	a.header = views.NewHeader(a.config, a.theme)

	// Create navigation component with 8 modules as specified in fyne.md
	a.navigation = views.NewNavigation(a.theme, a.onModuleSelected)

	// Create main content area
	a.content = views.NewMainContent(a.theme)

	// Create status bar
	a.statusBar = views.NewStatusBar(a.config, a.theme)

	// Initialize with default module
	a.switchToModule(a.currentModule)

	a.logger.Info("GUI components initialized successfully")
	return nil
}

// setupLayout creates the main application layout following fyne.md structure
func (a *App) setupLayout() {
	// Main layout structure as specified in fyne.md:
	// +----------------------------------------------------------+
	// | App Header (56px)                                         |
	// +----------------------------------------------------------+
	// | Module Navigation (64px)                                  |
	// +----------------------------------------------------------+
	// | Module Content Area (flex)                                |
	// +----------------------------------------------------------+
	// | Status Bar (32px)                                         |
	// +----------------------------------------------------------+

	mainLayout := container.NewBorder(
		// Top: Header + Navigation
		container.NewVBox(
			a.header.Content(),
			a.navigation.Content(),
		),
		// Bottom: Status Bar
		a.statusBar.Content(),
		// Left: nil
		nil,
		// Right: nil
		nil,
		// Center: Main Content Area
		a.content.Content(),
	)

	a.window.SetContent(mainLayout)
}

// onModuleSelected handles module selection from navigation
func (a *App) onModuleSelected(moduleName string) {
	a.logger.Info("Module selected", "module", moduleName)
	a.switchToModule(moduleName)
}

// switchToModule switches the current module and updates the content area
func (a *App) switchToModule(moduleName string) {
	// Use fyne.Do to ensure thread safety for UI updates
	fyne.Do(func() {
		a.currentModule = moduleName
		a.navigation.SetActiveModule(moduleName)

		// Get module content based on the module name
		moduleContent := a.getModuleContent(moduleName)
		a.content.SetContent(moduleContent)

		// Update status bar
		a.statusBar.SetMessage(fmt.Sprintf("Switched to %s module", moduleName), "info")

		a.logger.Info("Switched to module", "module", moduleName)
	})
}

// getModuleContent returns the content for the specified module
func (a *App) getModuleContent(moduleName string) fyne.CanvasObject {
	switch moduleName {
	case "dashboard":
		return a.getDashboardContent()
	case "database":
		return a.getDatabaseContent()
	case "k8s":
		return a.getK8sContent()
	case "docker":
		return a.getDockerContent()
	case "tasks":
		return a.getTasksContent()
	case "mcp":
		return a.getMCPContent()
	case "search":
		return a.getSearchContent()
	case "chat":
		return a.getChatContent()
	case "agent":
		return a.getAgentContent()
	default:
		return a.getPlaceholderContent(moduleName)
	}
}

// getDashboardContent returns the main dashboard/home screen
func (a *App) getDashboardContent() fyne.CanvasObject {
	// Create module status cards
	moduleStatuses := container.NewGridWithColumns(4)

	// Get health status for all modules
	healthData := make(map[string]string)
	for name, module := range a.modules {
		// Try to get health status if module supports it
		if healthModule, ok := module.(interface{ Health() types.ModuleHealth }); ok {
			health := healthModule.Health()
			healthData[name] = health.Status
		} else {
			healthData[name] = "unknown"
		}
	}

	// Create status cards for each module
	modules := []struct{ name, display, icon string }{
		{"postgres", "Database", "🗄️"},
		{"k8s", "Kubernetes", "☸️"},
		{"docker", "Docker", "🐳"},
		{"tasks", "Tasks", "📋"},
		{"mcp", "MCP", "🔗"},
		{"search", "Search", "🔍"},
		{"ai", "Chat", "💬"},
		{"ai", "Agent", "🤖"},
	}

	for _, mod := range modules {
		status := "Unknown"
		if s, exists := healthData[mod.name]; exists {
			status = s
		}

		statusColor := "🔴" // Default red
		if status == "healthy" {
			statusColor = "🟢"
		} else if status == "degraded" {
			statusColor = "🟡"
		}

		card := widget.NewCard(
			mod.icon+" "+mod.display,
			"Module Status",
			widget.NewLabel(statusColor+" "+status),
		)
		moduleStatuses.Add(card)
	}

	// Create main dashboard content with proper Material Design 3 spacing
	dashboardContent := container.NewVBox(
		// Header section with SpaceLG (24px) padding
		container.NewPadded(
			widget.NewRichTextFromMarkdown(`# 🚀 Assistant-Go Dashboard

**CYBERPUNK DEVELOPMENT ENVIRONMENT**

> Welcome to your personal development assistant with modular architecture.
> Monitor system status, access tools, and manage your development workflow.

---`),
		),

		// System overview section with SpaceMD (16px) spacing
		container.NewVBox(
			container.NewPadded(widget.NewLabel("System Overview")),
			moduleStatuses,
		),

		// Spacer for visual separation (SpaceLG = 24px)
		widget.NewSeparator(),

		// Quick actions section with proper card elevation
		container.NewPadded(
			widget.NewCard(
				"🎯 Quick Actions",
				"Common development tasks",
				container.NewGridWithColumns(3,
					cybertheme.NewCyberButtonWithVariant("📋 Run Tasks", cybertheme.ButtonPrimary, func() {
						a.switchToModule("tasks")
					}),
					cybertheme.NewCyberButtonWithVariant("🐳 Manage Containers", cybertheme.ButtonPrimary, func() {
						a.switchToModule("docker")
					}),
					cybertheme.NewCyberButtonWithVariant("🗄️ Database Tools", cybertheme.ButtonPrimary, func() {
						a.switchToModule("database")
					}),
				),
			),
		),

		// System information section with proper spacing
		container.NewPadded(
			widget.NewCard(
				"📊 System Information",
				"Current environment status",
				container.NewVBox(
					widget.NewLabel("Environment: "+a.config.App.Environment),
					widget.NewLabel("Version: "+a.config.App.Version),
					widget.NewLabel("Theme: Cyberpunk Blue-Dominant"),
					widget.NewLabel("Modules: 8 Available"),
				),
			),
		),
	)

	return container.NewScroll(dashboardContent)
}

// getDatabaseContent returns the PostgreSQL module content
func (a *App) getDatabaseContent() fyne.CanvasObject {
	if module, exists := a.modules["postgres"]; exists {
		// Try to get UI from the postgres module
		if postgresModule, ok := module.(interface{ GetUI() fyne.CanvasObject }); ok {
			return postgresModule.GetUI()
		}
	}
	return a.getPlaceholderContent("Database")
}

// getK8sContent returns the Kubernetes module content
func (a *App) getK8sContent() fyne.CanvasObject {
	if module, exists := a.modules["k8s"]; exists {
		// Try to get UI from the k8s module
		if k8sModule, ok := module.(interface{ GetUI() fyne.CanvasObject }); ok {
			return k8sModule.GetUI()
		}
	}
	return a.getPlaceholderContent("Kubernetes")
}

// getDockerContent returns the Docker module content
func (a *App) getDockerContent() fyne.CanvasObject {
	if module, exists := a.modules["docker"]; exists {
		// Try to get UI from the docker module
		if dockerModule, ok := module.(interface{ GetUI() fyne.CanvasObject }); ok {
			return dockerModule.GetUI()
		}
	}
	return a.getPlaceholderContent("Docker")
}

// getTasksContent returns the Tasks module content
func (a *App) getTasksContent() fyne.CanvasObject {
	if module, exists := a.modules["tasks"]; exists {
		// Try to get UI from the tasks module
		if tasksModule, ok := module.(interface{ GetUI() fyne.CanvasObject }); ok {
			return tasksModule.GetUI()
		}
	}
	return a.getPlaceholderContent("Tasks")
}

// getMCPContent returns the MCP module content
func (a *App) getMCPContent() fyne.CanvasObject {
	if module, exists := a.modules["mcp"]; exists {
		// Try to get UI from the mcp module
		if mcpModule, ok := module.(interface{ GetUI() fyne.CanvasObject }); ok {
			return mcpModule.GetUI()
		}
	}
	return a.getPlaceholderContent("MCP")
}

// getSearchContent returns the Search module content
func (a *App) getSearchContent() fyne.CanvasObject {
	if module, exists := a.modules["search"]; exists {
		// Try to get UI from the search module
		if searchModule, ok := module.(interface{ GetUI() fyne.CanvasObject }); ok {
			return searchModule.GetUI()
		}
	}
	return a.getPlaceholderContent("Search")
}

// getChatContent returns the Chat module content
func (a *App) getChatContent() fyne.CanvasObject {
	if module, exists := a.modules["ai"]; exists {
		// Try to get UI from the ai module for chat
		if aiModule, ok := module.(interface{ GetUI() fyne.CanvasObject }); ok {
			return aiModule.GetUI()
		}
	}
	// Create a basic chat interface if AI module doesn't have UI
	return a.createChatInterface()
}

// getAgentContent returns the Agent module content
func (a *App) getAgentContent() fyne.CanvasObject {
	if module, exists := a.modules["ai"]; exists {
		// Try to get UI from the ai module for agent
		if aiModule, ok := module.(interface{ GetUI() fyne.CanvasObject }); ok {
			return aiModule.GetUI()
		}
	}
	// Create a basic agent interface if AI module doesn't have UI
	return a.createAgentInterface()
}

// getPlaceholderContent creates a placeholder content for modules not yet implemented
func (a *App) getPlaceholderContent(moduleName string) fyne.CanvasObject {
	content := container.NewVBox(
		widget.NewCard(
			fmt.Sprintf("🚧 %s Module", moduleName),
			"This module is under development",
			widget.NewLabel(fmt.Sprintf("The %s module will be implemented according to the specifications in fyne.md", moduleName)),
		),
		widget.NewSeparator(),
		widget.NewRichTextFromMarkdown(fmt.Sprintf(`
## %s Module Status

This module is currently being developed following the Material Design 3 principles and cyberpunk aesthetic specified in fyne.md.

### Planned Features:
- Full Material Design 3 compliance
- Cyberpunk theme integration
- Real-time data updates
- Responsive layout design
- Proper threading with fyne.Do()
- Bidirectional scrolling support

### Implementation Progress:
- [ ] Core functionality
- [ ] UI components
- [ ] Data integration
- [ ] Testing and validation

Stay tuned for updates!
		`, moduleName)),
	)

	// Wrap in scroll container for overflow handling
	return container.NewScroll(content)
}

// createChatInterface creates a basic chat interface
func (a *App) createChatInterface() fyne.CanvasObject {
	// Create chat history
	chatHistory := widget.NewList(
		func() int { return 3 }, // Sample messages
		func() fyne.CanvasObject {
			return container.NewVBox(
				widget.NewLabel("Message"),
				widget.NewLabel("Timestamp"),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			messages := []struct{ content, time string }{
				{"Welcome to the AI Chat Assistant! How can I help you today?", "15:30:45"},
				{"I can help with code analysis, debugging, and development tasks.", "15:30:46"},
				{"Type your message below to start a conversation.", "15:30:47"},
	}
			if id < len(messages) {
				cont := obj.(*fyne.Container)
				cont.Objects[0].(*widget.Label).SetText("🤖 AI: " + messages[id].content)
				cont.Objects[1].(*widget.Label).SetText(messages[id].time)
			}
		},
	)

	// Create input area
	messageInput := widget.NewMultiLineEntry()
	messageInput.SetPlaceHolder("Type your message here...")
	messageInput.Resize(fyne.NewSize(600, 80))

	sendButton := cybertheme.NewCyberButtonWithVariant("📤 Send", cybertheme.ButtonPrimary, func() {
		// TODO: Implement actual chat functionality
		a.SetStatusMessage("Chat functionality coming soon!", "info")
	})

	chatContent := container.NewVBox(
		widget.NewRichTextFromMarkdown(`# 💬 AI Chat Assistant

**NATURAL LANGUAGE INTERFACE**

> Intelligent conversation with context-aware AI assistant.
> Chat functionality will be fully implemented with AI integration.

---`),
		widget.NewLabel("Chat History:"),
		container.NewScroll(chatHistory),
		widget.NewSeparator(),
		widget.NewLabel("Message Input:"),
		messageInput,
		container.NewHBox(sendButton),
	)

	return container.NewScroll(chatContent)
}

// createAgentInterface creates a basic agent interface
func (a *App) createAgentInterface() fyne.CanvasObject {
	// Agent capabilities
	capabilities := container.NewVBox(
		widget.NewLabel("🔧 Code Analysis & Review"),
		widget.NewLabel("🐛 Debugging Assistance"),
		widget.NewLabel("⚡ Performance Optimization"),
		widget.NewLabel("📚 Documentation Generation"),
		widget.NewLabel("🧪 Test Case Creation"),
		widget.NewLabel("🔍 Security Scanning"),
	)

	// Agent status
	agentStatus := container.NewVBox(
		widget.NewLabel("Status: Ready"),
		widget.NewLabel("Model: Claude-3.5-Sonnet"),
		widget.NewLabel("Context: Development Assistant"),
		widget.NewLabel("Tools: 8 Available"),
	)

	agentContent := container.NewVBox(
		widget.NewRichTextFromMarkdown(`# 🤖 AI Agent Control

**AUTONOMOUS DEVELOPMENT ASSISTANT**

> Advanced AI agent with tool integration and autonomous task execution.
> Monitor agent activities and configure autonomous behaviors.

---`),
		container.NewGridWithColumns(2,
			widget.NewCard("🎯 Agent Capabilities", "Available functions", capabilities),
			widget.NewCard("📊 Agent Status", "Current state", agentStatus),
		),
		widget.NewSeparator(),
		widget.NewCard(
			"🚀 Quick Actions",
			"Agent control panel",
			container.NewGridWithColumns(3,
				cybertheme.NewCyberButtonWithVariant("🔍 Analyze Code", cybertheme.ButtonPrimary, func() {
					a.SetStatusMessage("Code analysis feature coming soon!", "info")
				}),
				cybertheme.NewCyberButtonWithVariant("🧪 Generate Tests", cybertheme.ButtonSecondary, func() {
					a.SetStatusMessage("Test generation feature coming soon!", "info")
				}),
				cybertheme.NewCyberButtonWithVariant("📚 Create Docs", cybertheme.ButtonSecondary, func() {
					a.SetStatusMessage("Documentation feature coming soon!", "info")
				}),
			),
		),
	)

	return container.NewScroll(agentContent)
}

// ShowAndRun displays the window and starts the GUI event loop
func (a *App) ShowAndRun() {
	a.logger.Info("Starting GUI application...")
	a.window.ShowAndRun()
}

// Close gracefully closes the GUI application
func (a *App) Close() {
	a.logger.Info("Closing GUI application...")
	a.window.Close()
}

// GetCurrentModule returns the currently active module
func (a *App) GetCurrentModule() string {
	return a.currentModule
}

// SetStatusMessage updates the status bar message
func (a *App) SetStatusMessage(message, level string) {
	if a.statusBar != nil {
		a.statusBar.SetMessage(message, level)
	}
}

// GetWindow returns the main window
func (a *App) GetWindow() fyne.Window {
	return a.window
}

// GetTheme returns the cyberpunk theme
func (a *App) GetTheme() *cybertheme.CyberTheme {
	return a.theme
}
