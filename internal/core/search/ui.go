// Package search - UI component for Search module
// Implements the cyberpunk-themed interface for SearXNG search management
package search

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"assistant-go/internal/config"
	"assistant-go/pkg/cybertheme"
)

// UI represents the Search module UI
type UI struct {
	manager *Manager
	config  *config.Config
	theme   *cybertheme.CyberTheme

	// UI components
	content         *container.Scroll
	searchInput     *widget.Entry
	searchButton    *cybertheme.CyberButton
	categoryFilters *fyne.Container
	resultsList     *widget.List
	aiSummary       *fyne.Container
	statusLabel     *widget.Label

	// Search state
	currentQuery     string
	searchResults    []SearchResult
	activeCategories map[string]bool
	isSearching      bool
}

// SearchResult represents a search result
type SearchResult struct {
	Title       string
	URL         string
	Description string
	Source      string
	Category    string
}

// NewUI creates a new Search UI component
func NewUI(manager *Manager, cfg *config.Config, theme *cybertheme.CyberTheme) *UI {
	ui := &UI{
		manager:         manager,
		config:          cfg,
		theme:           theme,
		searchResults:   make([]SearchResult, 0),
		activeCategories: map[string]bool{
			"Web":    true,
			"Images": false,
			"Videos": false,
			"News":   true,
			"Maps":   false,
			"IT":     true,
		},
		isSearching: false,
	}

	ui.createComponents()
	return ui
}

// createComponents creates all UI components
func (ui *UI) createComponents() {
	// Create search input
	ui.createSearchInput()

	// Create category filters
	ui.createCategoryFilters()

	// Create results list
	ui.createResultsList()

	// Create AI summary panel
	ui.createAISummary()

	// Create status label
	ui.statusLabel = widget.NewLabel(">>> SEARXNG READY <<<")

	// Create main layout
	ui.createMainLayout()
}

// createSearchInput creates the search input area
func (ui *UI) createSearchInput() {
	ui.searchInput = widget.NewEntry()
	ui.searchInput.SetPlaceHolder("Enter search query...")
	ui.searchInput.Resize(fyne.NewSize(500, 40))

	// Handle Enter key to search
	ui.searchInput.OnSubmitted = func(query string) {
		if !ui.isSearching && query != "" {
			ui.performSearch(query)
		}
	}

	ui.searchButton = cybertheme.NewCyberButtonWithVariant("🔍 Search", cybertheme.ButtonPrimary, func() {
		if !ui.isSearching && ui.searchInput.Text != "" {
			ui.performSearch(ui.searchInput.Text)
		}
	})
}

// createCategoryFilters creates the category filter toggles
func (ui *UI) createCategoryFilters() {
	categoriesLabel := widget.NewLabel("Categories:")
	categoriesLabel.TextStyle = fyne.TextStyle{Bold: true}

	categoryButtons := container.NewHBox()

	for category, active := range ui.activeCategories {
		variant := cybertheme.ButtonSecondary
		if active {
			variant = cybertheme.ButtonPrimary
		}

		btn := cybertheme.NewCyberButtonWithVariant(category, variant, ui.createCategoryToggle(category))
		btn.SetSize(cybertheme.ButtonSmall)
		categoryButtons.Add(btn)
	}

	ui.categoryFilters = container.NewVBox(
		categoriesLabel,
		categoryButtons,
	)
}

// createResultsList creates the search results list
func (ui *UI) createResultsList() {
	ui.resultsList = widget.NewList(
		func() int {
			return len(ui.searchResults)
		},
		func() fyne.CanvasObject {
			return container.NewVBox(
				widget.NewLabel("Title"),
				widget.NewLabel("URL"),
				widget.NewLabel("Description"),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			if id >= len(ui.searchResults) {
				return
			}

			result := ui.searchResults[id]
			cont := obj.(*fyne.Container)
			titleLabel := cont.Objects[0].(*widget.Label)
			urlLabel := cont.Objects[1].(*widget.Label)
			descLabel := cont.Objects[2].(*widget.Label)

			titleLabel.SetText(result.Title)
			titleLabel.TextStyle = fyne.TextStyle{Bold: true}

			urlLabel.SetText(result.URL)
			urlLabel.TextStyle = fyne.TextStyle{Monospace: true}

			descLabel.SetText(result.Description)
		},
	)
}

// createAISummary creates the AI summary panel
func (ui *UI) createAISummary() {
	summaryLabel := widget.NewLabel("🤖 AI Analysis")
	summaryLabel.TextStyle = fyne.TextStyle{Bold: true}

	ui.aiSummary = container.NewVBox(
		summaryLabel,
		widget.NewSeparator(),
		widget.NewLabel("Search for information to see AI analysis"),
		widget.NewLabel("Key insights will appear here"),
	)
}

// createMainLayout creates the main UI layout following fyne.md specifications
func (ui *UI) createMainLayout() {
	// Create search header
	searchHeader := container.NewVBox(
		widget.NewRichTextFromMarkdown(`# 🔍 Privacy-Focused Search

**SEARXNG INTEGRATION WITH AI ENHANCEMENT**

> Secure web search with intelligent result analysis and summarization.
> Privacy-first approach with no tracking or data collection.

---`),
		container.NewHBox(ui.searchInput, ui.searchButton),
		ui.categoryFilters,
		widget.NewSeparator(),
	)

	// Create main two-panel layout (70% results, 30% AI summary)
	resultsPanel := container.NewVBox(
		widget.NewLabel("🔍 Search Results"),
		ui.resultsList,
	)

	resultsScroll := container.NewScroll(resultsPanel)
	resultsScroll.SetMinSize(fyne.NewSize(500, 400))

	summaryScroll := container.NewScroll(ui.aiSummary)
	summaryScroll.SetMinSize(fyne.NewSize(300, 400))

	mainPanels := container.NewHSplit(resultsScroll, summaryScroll)
	mainPanels.SetOffset(0.7) // 70% for results, 30% for AI summary

	// Complete layout
	completeLayout := container.NewVBox(
		searchHeader,
		mainPanels,
		widget.NewSeparator(),
		ui.statusLabel,
	)

	// Wrap in scroll container to handle overflow
	ui.content = container.NewScroll(completeLayout)
	ui.content.SetMinSize(fyne.NewSize(1000, 700))
}

// Content returns the UI content
func (ui *UI) Content() fyne.CanvasObject {
	return ui.content
}

// Refresh updates the UI content
func (ui *UI) Refresh() {
	if ui.content != nil {
		ui.content.Refresh()
	}
}

// performSearch executes a search query
func (ui *UI) performSearch(query string) {
	ui.currentQuery = query
	ui.isSearching = true
	ui.statusLabel.SetText(">>> Searching... <<<")
	ui.searchButton.Disable()

	// Simulate search (replace with actual SearXNG integration)
	go func() {
		// Use fyne.Do for thread-safe UI updates
		fyne.Do(func() {
			// Add mock results
			ui.searchResults = []SearchResult{
				{
					Title:       "Go Programming Best Practices",
					URL:         "https://golang.org/doc/effective_go.html",
					Description: "Official Go documentation on effective programming techniques.",
					Source:      "golang.org",
					Category:    "Web",
				},
				{
					Title:       "Fyne Framework Tutorial",
					URL:         "https://developer.fyne.io/tutorial/",
					Description: "Complete tutorial for building cross-platform apps with Fyne.",
					Source:      "developer.fyne.io",
					Category:    "IT",
				},
			}

			// Reset state
			ui.isSearching = false
			ui.statusLabel.SetText(">>> Search completed <<<")
			ui.searchButton.Enable()
			ui.resultsList.Refresh()
		})
	}()
}

// createCategoryToggle creates a toggle function for category filters
func (ui *UI) createCategoryToggle(category string) func() {
	return func() {
		ui.activeCategories[category] = !ui.activeCategories[category]
		ui.createCategoryFilters()
		ui.statusLabel.SetText(">>> Category " + category + " toggled <<<")
	}
}
