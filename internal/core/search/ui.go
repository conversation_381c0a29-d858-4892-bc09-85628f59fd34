// Package search - UI component for Search module
// Implements the cyberpunk-themed interface for SearXNG search management
package search

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"assistant-go/internal/config"
	"assistant-go/pkg/cybertheme"
)

// UI represents the Search module UI
type UI struct {
	manager *Manager
	config  *config.Config
	theme   *cybertheme.CyberTheme

	// UI components
	content *fyne.Container
}

// NewUI creates a new Search UI component
func NewUI(manager *Manager, cfg *config.Config, theme *cybertheme.CyberTheme) *UI {
	ui := &UI{
		manager: manager,
		config:  cfg,
		theme:   theme,
	}

	ui.createContent()
	return ui
}

// createContent creates the main UI content
func (ui *UI) createContent() {
	// Create main content with cyberpunk styling
	title := widget.NewCard(
		"🔍 SearXNG Search Engine",
		"Advanced search capabilities with cyberpunk aesthetics",
		widget.NewLabel("Search the web using SearXNG with privacy-focused, aggregated results."),
	)

	// Search status
	statusCard := widget.NewCard(
		"Search Status",
		"Current search engine information",
		container.NewVBox(
			widget.NewLabel("Status: Connected"),
			widget.NewLabel("Engines: 15 Active"),
			widget.NewLabel("Categories: 10 Available"),
			widget.NewLabel("Queries Today: 42"),
			widget.NewProgressBarInfinite(),
		),
	)

	// Quick actions
	actionsCard := widget.NewCard(
		"Quick Actions",
		"Common search operations",
		container.NewVBox(
			cybertheme.NewCyberButton("🔍 New Search", func() {
				// TODO: Open search interface
			}),
			cybertheme.NewCyberButton("📊 Search Analytics", func() {
				// TODO: Open search analytics
			}),
			cybertheme.NewCyberButton("⚙️ Engine Config", func() {
				// TODO: Open engine configuration
			}),
			cybertheme.NewCyberButton("📝 Search History", func() {
				// TODO: Open search history
			}),
		),
	)

	// Recent searches
	recentCard := widget.NewCard(
		"Recent Searches",
		"Latest search queries",
		container.NewVBox(
			widget.NewLabel("🔍 \"golang best practices\" - 127 results"),
			widget.NewLabel("🔍 \"cyberpunk UI design\" - 89 results"),
			widget.NewLabel("🔍 \"fyne framework tutorial\" - 156 results"),
		),
	)

	// Main layout
	ui.content = container.NewVBox(
		title,
		container.NewGridWithColumns(2,
			statusCard,
			actionsCard,
		),
		recentCard,
		widget.NewCard(
			"🚧 Under Development",
			"This module is being enhanced",
			widget.NewRichTextFromMarkdown(`
## Search Module Features

This module will provide comprehensive SearXNG search engine integration:

### Core Features:
- **Multi-Engine Search**: Aggregate results from multiple search engines
- **Privacy-Focused**: No tracking, no data collection, anonymous searching
- **Category Filtering**: Search specific categories (web, images, videos, news)
- **Engine Selection**: Choose specific search engines for targeted results
- **Real-time Results**: Fast, responsive search with live result updates

### Advanced Features:
- **Search Analytics**: Track search patterns and popular queries
- **Custom Engines**: Add and configure custom search engines
- **Result Filtering**: Advanced filtering and sorting options
- **Search History**: Persistent search history with favorites
- **API Integration**: RESTful API for programmatic search access

### Cyberpunk Aesthetics:
- Material Design 3 compliance with blue-dominant color scheme
- Real-time search status indicators with animated elements
- Responsive layout with proper scrolling support
- Pure white bold text for maximum contrast and readability

Stay tuned for the full implementation!
			`),
		),
	)
}

// Content returns the UI content
func (ui *UI) Content() fyne.CanvasObject {
	return container.NewScroll(ui.content)
}

// Refresh updates the UI content
func (ui *UI) Refresh() {
	if ui.content != nil {
		ui.content.Refresh()
	}
}
