// Package tasks provides the UI components for task management
// Implements cyberpunk-styled task interface with real-time execution
package tasks

import (
	"fmt"
	"strings"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"

	"assistant-go/internal/config"
	"assistant-go/pkg/cybertheme"
)

// UI provides the user interface for task management
type UI struct {
	manager *Manager
	config  *config.Config
	theme   *cybertheme.CyberTheme

	// Main components
	content         *container.Scroll
	taskList        *widget.List
	favoritesList   *widget.List
	outputText      *widget.Entry
	statusLabel     *widget.Label
	taskInfoSection *fyne.Container

	// Task info display components
	taskNameLabel        *widget.Label
	taskStatusLabel      *widget.Label
	taskProgressBar      *widget.ProgressBar
	taskDurationLabel    *widget.Label
	taskCommandLabel     *widget.Label

	// Current state
	selectedTask   string
	allTasks       map[string]*Task
	favorites      []string
	filteredTasks  []*Task
	currentFilter  string
	currentSource  string
	isExecuting    bool
}

// NewUI creates a new task management UI
func NewUI(manager *Manager, cfg *config.Config, theme *cybertheme.CyberTheme) *UI {
	ui := &UI{
		manager:       manager,
		config:        cfg,
		theme:         theme,
		currentSource: "Both",
		currentFilter: "",
		isExecuting:   false,
	}

	ui.createComponents()
	ui.refreshTasks()
	return ui
}

// createComponents creates all UI components
func (ui *UI) createComponents() {
	// Create task list
	ui.createTaskList()

	// Create favorites list
	ui.createFavoritesList()

	// Create output display
	ui.createOutputDisplay()

	// Create status label
	ui.statusLabel = widget.NewLabel(">>> TASK MODULE READY <<<")

	// Create main layout
	ui.createMainLayout()
}

// createTaskList creates the main task list
func (ui *UI) createTaskList() {
	ui.taskList = widget.NewList(
		func() int {
			return len(ui.filteredTasks)
		},
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewLabel("📄"), // Source icon
				widget.NewLabel("Task Name"),
				widget.NewLabel("Description"),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			if id >= len(ui.filteredTasks) {
				return
			}

			task := ui.filteredTasks[id]
			cont := obj.(*fyne.Container)

			// Update icon based on task source
			iconLabel := cont.Objects[0].(*widget.Label)
			if task.Source == "makefile" {
				iconLabel.SetText("🔧") // Makefile icon
			} else {
				iconLabel.SetText("⚙️") // Taskfile icon
			}

			// Update task name
			nameLabel := cont.Objects[1].(*widget.Label)
			nameLabel.SetText(task.Name)
			nameLabel.TextStyle = fyne.TextStyle{Bold: true}

			// Update description
			descLabel := cont.Objects[2].(*widget.Label)
			description := task.Description
			if description == "" {
				description = "No description"
			}
			if len(description) > 40 {
				description = description[:40] + "..."
			}
			descLabel.SetText(description)
		},
	)

	ui.taskList.OnSelected = func(id widget.ListItemID) {
		if id < len(ui.filteredTasks) {
			ui.selectedTask = ui.filteredTasks[id].Name
			ui.updateTaskInfo(ui.selectedTask)
			ui.updateStatus(fmt.Sprintf("Selected task: %s", ui.selectedTask))
		}
	}
}

// createFavoritesList creates the favorites list
func (ui *UI) createFavoritesList() {
	ui.favoritesList = widget.NewList(
		func() int {
			return len(ui.favorites)
		},
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewIcon(nil), // TODO: Add favorite icon
				widget.NewLabel("Favorite Task"),
				widget.NewButton("Run", nil),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			if id < len(ui.favorites) {
				taskName := ui.favorites[id]

				cont := obj.(*fyne.Container)
				nameLabel := cont.Objects[1].(*widget.Label)
				runButton := cont.Objects[2].(*widget.Button)

				nameLabel.SetText(taskName)
				runButton.OnTapped = func() {
					ui.executeTask(taskName)
				}
			}
		},
	)
}

// createOutputDisplay creates the output display area
func (ui *UI) createOutputDisplay() {
	ui.outputText = widget.NewMultiLineEntry()
	ui.outputText.SetPlaceHolder("Task output will appear here...")
	ui.outputText.Disable() // Read-only
	ui.outputText.Resize(fyne.NewSize(600, 300))
}

// createMainLayout creates the main UI layout following fyne.md specifications
func (ui *UI) createMainLayout() {
	// Create task source selector and filter
	taskSourceLabel := widget.NewLabel("Task Source:")
	taskSourceLabel.TextStyle = fyne.TextStyle{Bold: true}

	taskSourceSelect := widget.NewSelect([]string{"Makefile", "Taskfile", "Both"}, func(value string) {
		ui.onTaskSourceChanged(value)
	})
	taskSourceSelect.SetSelected("Both")

	reloadBtn := cybertheme.NewCyberButtonWithVariant("🔄 Reload", cybertheme.ButtonSecondary, ui.refreshTasks)
	reloadBtn.SetSize(cybertheme.ButtonSmall)

	filterEntry := widget.NewEntry()
	filterEntry.SetPlaceHolder("Filter tasks...")
	filterEntry.OnChanged = ui.onFilterChanged

	// Create task source header
	taskSourceHeader := container.NewHBox(
		taskSourceLabel,
		taskSourceSelect,
		reloadBtn,
		widget.NewSeparator(),
		widget.NewLabel("Filter:"),
		filterEntry,
	)

	// Create left panel - Available Tasks (40% width)
	leftPanelHeader := widget.NewLabel("📋 Available Tasks")
	leftPanelHeader.TextStyle = fyne.TextStyle{Bold: true}

	// Create favorites section
	favoritesLabel := widget.NewLabel("⭐ Favorites")
	favoritesLabel.TextStyle = fyne.TextStyle{Bold: true}

	favoriteActions := container.NewHBox(
		cybertheme.NewCyberButtonWithVariant("➕ Add", cybertheme.ButtonSecondary, ui.addToFavorites),
		cybertheme.NewCyberButtonWithVariant("➖ Remove", cybertheme.ButtonSecondary, ui.removeFromFavorites),
	)

	favoritesSection := container.NewVBox(
		favoritesLabel,
		ui.favoritesList,
		favoriteActions,
	)

	// Create all tasks section
	allTasksLabel := widget.NewLabel("📋 All Tasks")
	allTasksLabel.TextStyle = fyne.TextStyle{Bold: true}

	taskActions := container.NewHBox(
		cybertheme.NewCyberButtonWithVariant("▶️ Execute", cybertheme.ButtonPrimary, ui.executeSelectedTask),
		cybertheme.NewCyberButtonWithVariant("ℹ️ Details", cybertheme.ButtonSecondary, ui.showTaskDetails),
	)

	allTasksSection := container.NewVBox(
		allTasksLabel,
		ui.taskList,
		taskActions,
	)

	// Left panel content
	leftPanel := container.NewVBox(
		leftPanelHeader,
		widget.NewSeparator(),
		favoritesSection,
		widget.NewSeparator(),
		allTasksSection,
	)

	// Wrap left panel in scroll container
	leftPanelScroll := container.NewScroll(leftPanel)
	leftPanelScroll.SetMinSize(fyne.NewSize(400, 500))

	// Create right panel - Execution Panel (60% width)
	rightPanelHeader := widget.NewLabel("⚡ Execution Panel")
	rightPanelHeader.TextStyle = fyne.TextStyle{Bold: true}

	// Create task info section
	ui.createTaskInfoSection()

	// Create output section with better controls
	outputLabel := widget.NewLabel("📄 Output")
	outputLabel.TextStyle = fyne.TextStyle{Bold: true}

	outputActions := container.NewHBox(
		cybertheme.NewCyberButtonWithVariant("🗑️ Clear", cybertheme.ButtonSecondary, func() {
			ui.outputText.SetText("")
			ui.updateStatus("Output cleared")
		}),
		cybertheme.NewCyberButtonWithVariant("💾 Save", cybertheme.ButtonSecondary, ui.saveOutput),
		cybertheme.NewCyberButtonWithVariant("📋 Copy", cybertheme.ButtonSecondary, ui.copyOutput),
	)

	outputSection := container.NewVBox(
		outputLabel,
		container.NewScroll(ui.outputText),
		outputActions,
	)

	// Right panel content
	rightPanel := container.NewVBox(
		rightPanelHeader,
		widget.NewSeparator(),
		ui.taskInfoSection,
		widget.NewSeparator(),
		outputSection,
		widget.NewSeparator(),
		ui.statusLabel,
	)

	// Wrap right panel in scroll container
	rightPanelScroll := container.NewScroll(rightPanel)
	rightPanelScroll.SetMinSize(fyne.NewSize(600, 500))

	// Create main two-panel layout as specified in fyne.md (40% left, 60% right)
	mainPanels := container.NewHSplit(leftPanelScroll, rightPanelScroll)
	mainPanels.SetOffset(0.4) // 40% for left panel, 60% for right panel

	// Create complete layout with proper Material Design 3 spacing
	completeLayout := container.NewVBox(
		// Module header with proper spacing (SpaceLG = 24px)
		container.NewPadded(
			widget.NewRichTextFromMarkdown(`# 📋 Task Manager

**MAKEFILE & TASKFILE INTEGRATION**

> Direct access to your build tasks with intelligent management.
> Supports both Makefile and Taskfile with favorites and real-time execution.

---`),
		),
		// Control header with SpaceMD spacing (16px)
		container.NewPadded(taskSourceHeader),
		// Main content panels
		mainPanels,
	)

	// Wrap in scroll container with bidirectional scrolling
	ui.content = container.NewScroll(completeLayout)
	ui.content.SetMinSize(fyne.NewSize(1000, 700))
	ui.content.Direction = container.ScrollBoth // Enable both horizontal and vertical scrolling
}

// refreshTasks refreshes the task lists
func (ui *UI) refreshTasks() {
	// Reload tasks from manager
	ui.manager.ReloadTasks()

	// Update local state
	ui.allTasks = ui.manager.GetAllTasks()
	ui.favorites = ui.manager.GetFavorites()

	// Apply current filters
	ui.applyFilters()

	// Refresh UI components
	ui.taskList.Refresh()
	ui.favoritesList.Refresh()

	ui.updateStatus(fmt.Sprintf("Loaded %d tasks (%d favorites, %d filtered)",
		len(ui.allTasks), len(ui.favorites), len(ui.filteredTasks)))
}

// getTaskSlice returns tasks as a slice for list display
func (ui *UI) getTaskSlice() []*Task {
	tasks := make([]*Task, 0, len(ui.allTasks))
	for _, task := range ui.allTasks {
		tasks = append(tasks, task)
	}
	return tasks
}

// executeSelectedTask executes the currently selected task
func (ui *UI) executeSelectedTask() {
	if ui.selectedTask == "" {
		ui.updateStatus("Error: No task selected")
		return
	}

	ui.executeTask(ui.selectedTask)
}

// executeTask executes a specific task
func (ui *UI) executeTask(taskName string) {
	ui.isExecuting = true
	ui.updateStatus(fmt.Sprintf("Executing task: %s", taskName))
	ui.outputText.SetText(fmt.Sprintf(">>> Executing task: %s <<<\n", taskName))

	// Update task info to show execution state
	ui.updateTaskInfo(taskName)

	// Start time tracking
	startTime := time.Now()

	// Execute task in background
	go func() {
		execution, err := ui.manager.ExecuteTask(taskName)

		// Fix threading issue: wrap UI updates in fyne.Do()
		fyne.Do(func() {
			ui.isExecuting = false
			duration := time.Since(startTime)

			if err != nil {
				ui.updateStatus(fmt.Sprintf("Error executing task: %s", err.Error()))
				ui.appendOutput(fmt.Sprintf("Error: %s\n", err.Error()))
				ui.taskStatusLabel.SetText("Error")
				ui.taskProgressBar.Hide()
				ui.taskDurationLabel.SetText(fmt.Sprintf("Duration: %v (failed)", duration))
				return
			}

			// Update output
			ui.appendOutput(fmt.Sprintf("Task: %s\n", execution.Task.Name))
			ui.appendOutput(fmt.Sprintf("Duration: %v\n", execution.Duration))
			ui.appendOutput(fmt.Sprintf("Exit Code: %d\n", execution.ExitCode))
			ui.appendOutput("--- Output ---\n")
			ui.appendOutput(execution.Output)
			ui.appendOutput("\n--- End Output ---\n")

			// Update task info section
			ui.taskProgressBar.Hide()
			ui.taskDurationLabel.SetText(fmt.Sprintf("Duration: %v", execution.Duration))

			if execution.Error != nil {
				ui.updateStatus(fmt.Sprintf("Task failed: %s (exit code: %d)",
					taskName, execution.ExitCode))
				ui.taskStatusLabel.SetText(fmt.Sprintf("Failed (exit code: %d)", execution.ExitCode))
			} else {
				ui.updateStatus(fmt.Sprintf("Task completed: %s (%v)",
					taskName, execution.Duration))
				ui.taskStatusLabel.SetText("Completed ✓")
			}
		})
	}()
}

// appendOutput appends text to the output display
func (ui *UI) appendOutput(text string) {
	currentText := ui.outputText.Text
	ui.outputText.SetText(currentText + text)

	// Scroll to bottom
	ui.outputText.CursorRow = strings.Count(ui.outputText.Text, "\n")
}

// addToFavorites adds the selected task to favorites
func (ui *UI) addToFavorites() {
	if ui.selectedTask == "" {
		ui.updateStatus("Error: No task selected")
		return
	}

	if err := ui.manager.AddFavorite(ui.selectedTask); err != nil {
		ui.updateStatus(fmt.Sprintf("Error adding to favorites: %s", err.Error()))
		return
	}

	ui.refreshTasks()
	ui.updateStatus(fmt.Sprintf("Added '%s' to favorites", ui.selectedTask))
}

// removeFromFavorites removes the selected task from favorites
func (ui *UI) removeFromFavorites() {
	if ui.selectedTask == "" {
		ui.updateStatus("Error: No task selected")
		return
	}

	if err := ui.manager.RemoveFavorite(ui.selectedTask); err != nil {
		ui.updateStatus(fmt.Sprintf("Error removing from favorites: %s", err.Error()))
		return
	}

	ui.refreshTasks()
	ui.updateStatus(fmt.Sprintf("Removed '%s' from favorites", ui.selectedTask))
}

// showTaskDetails shows detailed information about the selected task
func (ui *UI) showTaskDetails() {
	if ui.selectedTask == "" {
		ui.updateStatus("Error: No task selected")
		return
	}

	task, err := ui.manager.GetTask(ui.selectedTask)
	if err != nil {
		ui.updateStatus(fmt.Sprintf("Error getting task details: %s", err.Error()))
		return
	}

	// Create details content
	details := fmt.Sprintf(`
**Task Details**

**Name:** %s
**Source:** %s
**Command:** %s
**Description:** %s
**Dependencies:** %s
**Last Run:** %s
**Run Count:** %d
`,
		task.Name,
		task.Source,
		task.Command,
		task.Description,
		strings.Join(task.Dependencies, ", "),
		formatTime(task.LastRun),
		task.RunCount,
	)

	detailsText := widget.NewRichTextFromMarkdown(details)

	// Show dialog
	dialog := dialog.NewCustom("Task Details", "Close",
		container.NewScroll(detailsText),
		fyne.CurrentApp().Driver().AllWindows()[0])
	dialog.Resize(fyne.NewSize(500, 400))
	dialog.Show()
}

// saveOutput saves the current output to a file
func (ui *UI) saveOutput() {
	if ui.outputText.Text == "" {
		ui.updateStatus("No output to save")
		return
	}

	// Create save dialog
	dialog := dialog.NewFileSave(func(writer fyne.URIWriteCloser, err error) {
		if err != nil {
			ui.updateStatus(fmt.Sprintf("Error saving file: %s", err.Error()))
			return
		}
		if writer == nil {
			return // User cancelled
		}
		defer writer.Close()

		if _, err := writer.Write([]byte(ui.outputText.Text)); err != nil {
			ui.updateStatus(fmt.Sprintf("Error writing file: %s", err.Error()))
			return
		}

		ui.updateStatus("Output saved successfully")
	}, fyne.CurrentApp().Driver().AllWindows()[0])

	dialog.SetFileName(fmt.Sprintf("task_output_%s.txt",
		time.Now().Format("20060102_150405")))
	dialog.Show()
}

// updateStatus updates the status label
func (ui *UI) updateStatus(message string) {
	ui.statusLabel.SetText(fmt.Sprintf(">>> %s <<<", message))
}

// formatTime formats a time for display
func formatTime(t time.Time) string {
	if t.IsZero() {
		return "Never"
	}
	return t.Format("2006-01-02 15:04:05")
}

// Content returns the UI content
func (ui *UI) Content() fyne.CanvasObject {
	return ui.content
}

// Refresh refreshes the UI
func (ui *UI) Refresh() {
	ui.refreshTasks()
	if ui.content != nil {
		ui.content.Refresh()
	}
}

// createTaskInfoSection creates the task information display section
func (ui *UI) createTaskInfoSection() {
	// Task name display
	ui.taskNameLabel = widget.NewLabel("No task selected")
	ui.taskNameLabel.TextStyle = fyne.TextStyle{Bold: true}

	// Task status display
	ui.taskStatusLabel = widget.NewLabel("Ready")

	// Progress bar for task execution
	ui.taskProgressBar = widget.NewProgressBar()
	ui.taskProgressBar.Hide() // Hidden by default

	// Duration display
	ui.taskDurationLabel = widget.NewLabel("Duration: --")
	ui.taskDurationLabel.TextStyle = fyne.TextStyle{Monospace: true}

	// Command display
	ui.taskCommandLabel = widget.NewLabel("Command: --")
	ui.taskCommandLabel.TextStyle = fyne.TextStyle{Monospace: true}

	// Create task info container
	ui.taskInfoSection = container.NewVBox(
		widget.NewLabel("📋 Current Task"),
		widget.NewSeparator(),
		ui.taskNameLabel,
		ui.taskStatusLabel,
		ui.taskProgressBar,
		ui.taskDurationLabel,
		ui.taskCommandLabel,
	)
}

// onTaskSourceChanged handles task source selection changes
func (ui *UI) onTaskSourceChanged(source string) {
	ui.currentSource = source
	ui.applyFilters()
	ui.updateStatus(fmt.Sprintf("Task source changed to: %s", source))
}

// onFilterChanged handles filter text changes
func (ui *UI) onFilterChanged(filter string) {
	ui.currentFilter = filter
	ui.applyFilters()
}

// applyFilters applies current filters to the task list
func (ui *UI) applyFilters() {
	ui.filteredTasks = make([]*Task, 0)

	for _, task := range ui.allTasks {
		// Apply source filter
		if ui.currentSource != "Both" {
			if ui.currentSource == "Makefile" && task.Source != "makefile" {
				continue
			}
			if ui.currentSource == "Taskfile" && task.Source != "taskfile" {
				continue
			}
		}

		// Apply text filter
		if ui.currentFilter != "" {
			if !strings.Contains(strings.ToLower(task.Name), strings.ToLower(ui.currentFilter)) &&
			   !strings.Contains(strings.ToLower(task.Description), strings.ToLower(ui.currentFilter)) {
				continue
			}
		}

		ui.filteredTasks = append(ui.filteredTasks, task)
	}

	// Refresh the task list
	ui.taskList.Refresh()
}

// copyOutput copies the current output to clipboard
func (ui *UI) copyOutput() {
	if ui.outputText.Text == "" {
		ui.updateStatus("No output to copy")
		return
	}

	// Get clipboard from current app
	clipboard := fyne.CurrentApp().Driver().AllWindows()[0].Clipboard()
	clipboard.SetContent(ui.outputText.Text)
	ui.updateStatus("Output copied to clipboard")
}

// updateTaskInfo updates the task information display
func (ui *UI) updateTaskInfo(taskName string) {
	if taskName == "" {
		ui.taskNameLabel.SetText("No task selected")
		ui.taskStatusLabel.SetText("Ready")
		ui.taskCommandLabel.SetText("Command: --")
		ui.taskProgressBar.Hide()
		return
	}

	task, err := ui.manager.GetTask(taskName)
	if err != nil {
		ui.taskNameLabel.SetText("Error loading task")
		ui.taskStatusLabel.SetText("Error")
		ui.taskCommandLabel.SetText("Command: --")
		return
	}

	ui.taskNameLabel.SetText(fmt.Sprintf("Task: %s", task.Name))
	ui.taskStatusLabel.SetText("Selected")
	ui.taskCommandLabel.SetText(fmt.Sprintf("Command: %s", task.Command))

	if ui.isExecuting {
		ui.taskProgressBar.Show()
		ui.taskStatusLabel.SetText("Executing...")
	} else {
		ui.taskProgressBar.Hide()
	}
}
