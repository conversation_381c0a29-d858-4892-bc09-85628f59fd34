// Package docker - UI component for Docker module
// Implements the cyberpunk-themed interface for Docker container management
package docker

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"assistant-go/internal/config"
	"assistant-go/pkg/cybertheme"
)

// UI represents the Docker module UI
type UI struct {
	manager *Manager
	config  *config.Config
	theme   *cybertheme.CyberTheme

	// UI components
	content *fyne.Container
}

// NewUI creates a new Docker UI component
func NewUI(manager *Manager, cfg *config.Config, theme *cybertheme.CyberTheme) *UI {
	ui := &UI{
		manager: manager,
		config:  cfg,
		theme:   theme,
	}

	ui.createContent()
	return ui
}

// createContent creates the main UI content following fyne.md specifications
func (ui *UI) createContent() {
	// Create search and filter header
	searchEntry := widget.NewEntry()
	searchEntry.SetPlaceHolder("Search containers...")

	filterSelect := widget.NewSelect([]string{"All", "Running", "Stopped", "Paused"}, func(value string) {
		ui.onFilterChanged(value)
	})
	filterSelect.SetSelected("All")

	addBtn := cybertheme.NewCyberButtonWithVariant("➕ New", cybertheme.ButtonPrimary, ui.onAddContainer)
	addBtn.SetSize(cybertheme.ButtonSmall)

	headerControls := container.NewHBox(
		widget.NewLabel("Search:"),
		searchEntry,
		widget.NewSeparator(),
		widget.NewLabel("Filter:"),
		filterSelect,
		widget.NewSeparator(),
		addBtn,
	)

	// Create left panel - Container List (40% width)
	leftPanelHeader := widget.NewLabel("🐳 Container List")
	leftPanelHeader.TextStyle = fyne.TextStyle{Bold: true}

	// Create container list
	containerList := ui.createContainerList()

	// Container actions
	containerActions := container.NewHBox(
		cybertheme.NewCyberButtonWithVariant("▶️ Start", cybertheme.ButtonPrimary, ui.onStartContainer),
		cybertheme.NewCyberButtonWithVariant("⏹️ Stop", cybertheme.ButtonSecondary, ui.onStopContainer),
		cybertheme.NewCyberButtonWithVariant("🔄 Restart", cybertheme.ButtonSecondary, ui.onRestartContainer),
		cybertheme.NewCyberButtonWithVariant("🗑️ Remove", cybertheme.ButtonDanger, ui.onRemoveContainer),
	)

	leftPanel := container.NewVBox(
		leftPanelHeader,
		widget.NewSeparator(),
		containerList,
		widget.NewSeparator(),
		containerActions,
	)

	// Wrap left panel in scroll container
	leftPanelScroll := container.NewScroll(leftPanel)
	leftPanelScroll.SetMinSize(fyne.NewSize(400, 500))

	// Create right panel - Container Details (60% width)
	rightPanelHeader := widget.NewLabel("📊 Container Details")
	rightPanelHeader.TextStyle = fyne.TextStyle{Bold: true}

	// Container info section
	containerInfo := ui.createContainerInfo()

	// Resource usage section
	resourceUsage := ui.createResourceUsage()

	// Logs section
	logsSection := ui.createLogsSection()

	rightPanel := container.NewVBox(
		rightPanelHeader,
		widget.NewSeparator(),
		containerInfo,
		widget.NewSeparator(),
		resourceUsage,
		widget.NewSeparator(),
		logsSection,
	)

	// Wrap right panel in scroll container
	rightPanelScroll := container.NewScroll(rightPanel)
	rightPanelScroll.SetMinSize(fyne.NewSize(600, 500))

	// Create main two-panel layout as specified in fyne.md (40% left, 60% right)
	mainPanels := container.NewHSplit(leftPanelScroll, rightPanelScroll)
	mainPanels.SetOffset(0.4) // 40% for left panel, 60% for right panel

	// Create complete layout with header
	completeLayout := container.NewVBox(
		widget.NewRichTextFromMarkdown(`# 🐳 Docker Container Management

**ADVANCED CONTAINER ORCHESTRATION**

> Real-time container management with cyberpunk aesthetics.
> Monitor, control, and optimize your Docker infrastructure.

---`),
		headerControls,
		widget.NewSeparator(),
		mainPanels,
	)

	ui.content = completeLayout
}

// Content returns the UI content
func (ui *UI) Content() fyne.CanvasObject {
	return container.NewScroll(ui.content)
}

// Refresh updates the UI content
func (ui *UI) Refresh() {
	if ui.content != nil {
		ui.content.Refresh()
	}
}
