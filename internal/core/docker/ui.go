// Package docker - UI component for Docker module
// Implements the cyberpunk-themed interface for Docker container management
package docker

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"assistant-go/internal/config"
	"assistant-go/pkg/cybertheme"
)

// UI represents the Docker module UI
type UI struct {
	manager *Manager
	config  *config.Config
	theme   *cybertheme.CyberTheme

	// UI components
	content *fyne.Container
}

// NewUI creates a new Docker UI component
func NewUI(manager *Manager, cfg *config.Config, theme *cybertheme.CyberTheme) *UI {
	ui := &UI{
		manager: manager,
		config:  cfg,
		theme:   theme,
	}

	ui.createContent()
	return ui
}

// createContent creates the main UI content
func (ui *UI) createContent() {
	// Create main content with cyberpunk styling
	title := widget.NewCard(
		"🐳 Docker Container Management",
		"Advanced container orchestration with cyberpunk aesthetics",
		widget.NewLabel("Manage Docker containers, images, networks, and volumes with style."),
	)

	// Docker status
	statusCard := widget.NewCard(
		"Docker Status",
		"Current Docker daemon information",
		container.NewVBox(
			widget.NewLabel("Status: Connected"),
			widget.NewLabel("Containers: 5 Running"),
			widget.NewLabel("Images: 23 Available"),
			widget.NewLabel("Networks: 3 Active"),
			widget.NewProgressBarInfinite(),
		),
	)

	// Quick actions
	actionsCard := widget.NewCard(
		"Quick Actions",
		"Common Docker operations",
		container.NewVBox(
			cybertheme.NewCyberButton("🚀 Run Container", func() {
				// TODO: Open container runner
			}),
			cybertheme.NewCyberButton("📦 Manage Images", func() {
				// TODO: Open image manager
			}),
			cybertheme.NewCyberButton("🌐 Network Config", func() {
				// TODO: Open network configuration
			}),
			cybertheme.NewCyberButton("💾 Volume Manager", func() {
				// TODO: Open volume manager
			}),
		),
	)

	// Recent activities
	recentCard := widget.NewCard(
		"Recent Activities",
		"Latest Docker operations",
		container.NewVBox(
			widget.NewLabel("🚀 Container nginx:latest started"),
			widget.NewLabel("📦 Image postgres:13 pulled"),
			widget.NewLabel("🌐 Network bridge_net created"),
		),
	)

	// Main layout
	ui.content = container.NewVBox(
		title,
		container.NewGridWithColumns(2,
			statusCard,
			actionsCard,
		),
		recentCard,
		widget.NewCard(
			"🚧 Under Development",
			"This module is being enhanced",
			widget.NewRichTextFromMarkdown(`
## Docker Module Features

This module will provide comprehensive Docker container management capabilities:

### Core Features:
- **Container Management**: Start, stop, restart, and remove containers
- **Image Management**: Pull, build, tag, and remove Docker images
- **Network Management**: Create and manage Docker networks
- **Volume Management**: Create and manage persistent storage volumes
- **Real-time Monitoring**: Live container stats and resource usage

### Advanced Features:
- **Docker Compose**: Deploy and manage multi-container applications
- **Registry Management**: Connect to and manage Docker registries
- **Container Logs**: Real-time log streaming and historical log viewing
- **Health Monitoring**: Container health checks and alerting
- **Resource Limits**: Configure CPU, memory, and storage limits

### Cyberpunk Aesthetics:
- Material Design 3 compliance with blue-dominant color scheme
- Real-time container status indicators with animated elements
- Responsive layout with proper scrolling support
- Pure white bold text for maximum contrast and readability

Stay tuned for the full implementation!
			`),
		),
	)
}

// Content returns the UI content
func (ui *UI) Content() fyne.CanvasObject {
	return container.NewScroll(ui.content)
}

// Refresh updates the UI content
func (ui *UI) Refresh() {
	if ui.content != nil {
		ui.content.Refresh()
	}
}
